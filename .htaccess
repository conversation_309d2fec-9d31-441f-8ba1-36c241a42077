# Menuz - إعدادات Apache للاستضافة المشتركة

# تفعيل mod_rewrite
RewriteEngine On

# فرض HTTPS (مفعل للاستضافة المشتركة)
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# منع الوصول للملفات الحساسة
<FilesMatch "\.(php|sql|log|txt)$">
    <RequireAll>
        Require all denied
    </RequireAll>
</FilesMatch>

# السماح بالوصول لملفات PHP الأساسية
<FilesMatch "^(index|install)\.php$">
    <RequireAll>
        Require all granted
    </RequireAll>
</FilesMatch>

# السماح بالوصول لملفات PHP في مجلدات محددة
<FilesMatch "^(auth|dashboard|public)/">
    <RequireAll>
        Require all granted
    </RequireAll>
</FilesMatch>

# منع الوصول للمجلدات الحساسة
<IfModule mod_rewrite.c>
    RewriteRule ^config/ - [F,L]
    RewriteRule ^database/ - [F,L]
    RewriteRule ^includes/ - [F,L]
</IfModule>

# إعادة توجيه الروابط الودية
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/(auth|dashboard|assets|uploads)/
RewriteRule ^([a-zA-Z0-9\-]+)/?$ index.php?restaurant=$1 [QSA,L]

# ضغط الملفات (محسن للاستضافة المشتركة)
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json

    # استثناء الملفات المضغوطة مسبقاً
    SetEnvIfNoCase Request_URI \
        \.(?:gif|jpe?g|png|zip|gz|bz2)$ no-gzip dont-vary
</IfModule>

# تخزين مؤقت للملفات الثابتة (محسن للاستضافة المشتركة)
<IfModule mod_expires.c>
    ExpiresActive On

    # الصور
    ExpiresByType image/jpg "access plus 30 days"
    ExpiresByType image/jpeg "access plus 30 days"
    ExpiresByType image/gif "access plus 30 days"
    ExpiresByType image/png "access plus 30 days"
    ExpiresByType image/webp "access plus 30 days"
    ExpiresByType image/svg+xml "access plus 30 days"
    ExpiresByType image/x-icon "access plus 1 year"

    # CSS و JavaScript
    ExpiresByType text/css "access plus 7 days"
    ExpiresByType application/javascript "access plus 7 days"
    ExpiresByType application/x-javascript "access plus 7 days"
    ExpiresByType text/javascript "access plus 7 days"

    # الخطوط
    ExpiresByType application/font-woff "access plus 30 days"
    ExpiresByType application/font-woff2 "access plus 30 days"
    ExpiresByType application/vnd.ms-fontobject "access plus 30 days"
    ExpiresByType font/truetype "access plus 30 days"

    # HTML و XML
    ExpiresByType text/html "access plus 1 hour"
    ExpiresByType application/xml "access plus 1 hour"
    ExpiresByType text/xml "access plus 1 hour"

    # افتراضي
    ExpiresDefault "access plus 1 day"
</IfModule>

# إعدادات الأمان المحسنة للاستضافة المشتركة
<IfModule mod_headers.c>
    # منع clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"

    # منع MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"

    # تفعيل XSS protection
    Header always set X-XSS-Protection "1; mode=block"

    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"

    # إخفاء معلومات الخادم
    Header unset Server
    Header unset X-Powered-By
    Header unset X-CF-Powered-By

    # Content Security Policy محسن
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' fonts.googleapis.com; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com data:; img-src 'self' data: https:; connect-src 'self'; frame-ancestors 'self';"

    # HSTS للأمان (فقط مع HTTPS)
    Header always set Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" env=HTTPS
</IfModule>

# منع عرض قائمة الملفات
Options -Indexes -MultiViews

# منع الوصول للملفات الحساسة بطريقة أخرى
<Files ~ "^\.">
    Require all denied
</Files>

# منع الوصول لملفات النسخ الاحتياطي
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Require all denied
</FilesMatch>

# إعدادات PHP محسنة للاستضافة المشتركة
<IfModule mod_php.c>
    php_value upload_max_filesize 10M
    php_value post_max_size 12M
    php_value max_execution_time 300
    php_value max_input_vars 3000
    php_value memory_limit 256M
    php_flag display_errors Off
    php_flag log_errors On
    php_flag expose_php Off
    php_flag allow_url_fopen Off
    php_flag allow_url_include Off
</IfModule>

# صفحات الأخطاء المخصصة (معطلة للاستضافة المشتركة)
# ErrorDocument 404 /404.php
# ErrorDocument 403 /403.php
# ErrorDocument 500 /500.php

# السماح بالوصول لـ .well-known (لـ SSL certificates)
<IfModule mod_rewrite.c>
    RewriteRule ^\.well-known/ - [L]
</IfModule>

# تحسين الأداء
<IfModule mod_rewrite.c>
    # إزالة trailing slash من الملفات
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_URI} (.+)/$
    RewriteRule ^ %1 [R=301,L]

    # إضافة trailing slash للمجلدات
    RewriteCond %{REQUEST_FILENAME} -d
    RewriteCond %{REQUEST_URI} !(.+)/$
    RewriteRule ^(.+)$ $1/ [R=301,L]
</IfModule>

# منع hotlinking للصور (محدث للدومين الجديد)
<IfModule mod_rewrite.c>
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?bag\.meedps\.com [NC]
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?meedps\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|webp|svg)$ - [NC,F,L]
</IfModule>

# إعدادات MIME types محسنة
<IfModule mod_mime.c>
    # JavaScript
    AddType application/javascript .js
    AddType application/json .json

    # CSS
    AddType text/css .css

    # الصور
    AddType image/svg+xml .svg
    AddType image/webp .webp

    # الخطوط
    AddType application/font-woff .woff
    AddType application/font-woff2 .woff2
    AddType application/vnd.ms-fontobject .eot
    AddType font/truetype .ttf
    AddType font/opentype .otf

    # أخرى
    AddType application/xml .xml
    AddType text/xml .xml
</IfModule>

# منع الوصول لملفات التكوين الإضافية
<FilesMatch "^(composer\.(json|lock)|package\.(json|lock)|\.env|\.git)">
    Require all denied
</FilesMatch>

# تحسين الأمان ضد البوتات الضارة
<IfModule mod_rewrite.c>
    # منع User Agents المشبوهة
    RewriteCond %{HTTP_USER_AGENT} ^$ [OR]
    RewriteCond %{HTTP_USER_AGENT} ^(java|curl|wget) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (libwww-perl|python|nikto|scan) [NC,OR]
    RewriteCond %{HTTP_USER_AGENT} (winhttp|HTTrack|clshttp|archiver|loader|email|harvest|extract|grab|miner) [NC]
    RewriteRule .* - [F,L]
</IfModule>
