/* تنسيقات صفحة المنيو */

.menu-page {
    background: #f8f9fa;
    min-height: 100vh;
}

/* هيدر المطعم */
.restaurant-header {
    background: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.cover-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.cover-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cover-image::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 50px;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
}

.restaurant-info {
    padding: 2rem 0;
}

.restaurant-details {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 1rem;
}

.restaurant-logo {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    flex-shrink: 0;
}

.restaurant-logo img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.restaurant-text {
    flex: 1;
}

.restaurant-name {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--restaurant-primary);
    margin-bottom: 0.5rem;
    line-height: 1.2;
}

.restaurant-description {
    font-size: 1.1rem;
    color: var(--text-light);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.restaurant-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-dark);
}

.meta-icon {
    font-size: 1.2rem;
}

.meta-item a {
    color: var(--restaurant-primary);
    text-decoration: none;
    font-weight: 500;
}

.meta-item a:hover {
    text-decoration: underline;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* تبديل اللغة */
.language-switcher {
    display: flex;
    background: var(--bg-light);
    border-radius: 25px;
    padding: 0.25rem;
    gap: 0.25rem;
}

.lang-btn {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.lang-btn.active {
    background: var(--restaurant-primary);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.lang-btn:hover:not(.active) {
    background: rgba(102, 126, 234, 0.1);
    color: var(--restaurant-primary);
}

/* سلة الطلبات */
.cart-btn {
    position: relative;
    background: var(--restaurant-primary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.cart-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.cart-icon {
    font-size: 1.2rem;
}

.cart-count {
    position: absolute;
    top: -5px;
    left: -5px;
    background: var(--error-color);
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: 600;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.cart-count.show {
    opacity: 1;
    transform: scale(1);
}

/* قائمة الفئات */
.categories-nav {
    background: white;
    border-bottom: 1px solid var(--border-color);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 99;
}

.categories-list {
    display: flex;
    gap: 1rem;
    overflow-x: auto;
    padding-bottom: 0.5rem;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.categories-list::-webkit-scrollbar {
    display: none;
}

.category-link {
    white-space: nowrap;
    padding: 0.75rem 1.5rem;
    background: var(--bg-light);
    color: var(--text-dark);
    text-decoration: none;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.category-link:hover,
.category-link.active {
    background: var(--restaurant-primary);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

/* محتوى المنيو */
.menu-content {
    padding: 2rem 0;
}

.category-section {
    margin-bottom: 3rem;
}

.category-header {
    text-align: center;
    margin-bottom: 2rem;
}

.category-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    position: relative;
    display: inline-block;
}

.category-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: var(--restaurant-primary);
    border-radius: 2px;
}

.category-description {
    color: var(--text-light);
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

/* شبكة المنتجات */
.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.product-card {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    cursor: pointer;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
}

.product-image {
    height: 200px;
    overflow: hidden;
    position: relative;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
    transform: scale(1.05);
}

.product-info {
    padding: 1.5rem;
}

.product-name {
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.product-description {
    color: var(--text-light);
    margin-bottom: 1rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.product-ingredients {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 1rem;
    padding: 0.5rem;
    background: var(--bg-light);
    border-radius: 8px;
}

.product-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.product-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--restaurant-primary);
}

.product-calories,
.product-time {
    font-size: 0.9rem;
    color: var(--text-light);
    background: var(--bg-light);
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
}

.add-to-cart-btn {
    width: 100%;
    background: var(--restaurant-primary);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.add-to-cart-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.add-to-cart-btn:hover::before {
    left: 100%;
}

.add-to-cart-btn:hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* سلة الطلبات الجانبية */
.cart-sidebar {
    position: fixed;
    top: 0;
    left: -400px;
    width: 400px;
    height: 100vh;
    background: white;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: left 0.3s ease;
    display: flex;
    flex-direction: column;
}

.cart-sidebar.open {
    left: 0;
}

.cart-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--restaurant-primary);
    color: white;
}

.cart-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.cart-close {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.cart-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.cart-items {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
}

.empty-cart {
    text-align: center;
    padding: 2rem;
    color: var(--text-light);
}

.cart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.cart-item-image {
    width: 60px;
    height: 60px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

.cart-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.cart-item-info {
    flex: 1;
}

.cart-item-name {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.cart-item-price {
    color: var(--restaurant-primary);
    font-weight: 600;
}

.cart-item-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border: 1px solid var(--border-color);
    background: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.quantity-btn:hover {
    background: var(--restaurant-primary);
    color: white;
    border-color: var(--restaurant-primary);
}

.quantity-display {
    min-width: 30px;
    text-align: center;
    font-weight: 600;
}

.cart-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    background: var(--bg-light);
}

.cart-total {
    margin-bottom: 1rem;
    text-align: center;
    font-size: 1.2rem;
}

.checkout-btn {
    width: 100%;
    background: var(--restaurant-primary);
    color: white;
    border: none;
    padding: 1rem;
    border-radius: 12px;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.checkout-btn:disabled {
    background: var(--text-light);
    cursor: not-allowed;
}

.checkout-btn:not(:disabled):hover {
    background: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.cart-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.cart-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* فوتر المنيو */
.menu-footer {
    background: var(--text-dark);
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
}

.menu-footer a {
    color: var(--restaurant-primary);
    text-decoration: none;
    font-weight: 600;
}

/* الاستجابة للأجهزة المحمولة */
@media (max-width: 768px) {
    .restaurant-details {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .restaurant-name {
        font-size: 2rem;
    }

    .restaurant-meta {
        justify-content: center;
    }

    .header-actions {
        justify-content: center;
        margin-top: 1rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .cart-sidebar {
        width: 100%;
        left: -100%;
    }

    .categories-list {
        padding: 0 1rem;
    }

    .cover-image {
        height: 150px;
    }

    .restaurant-logo {
        width: 60px;
        height: 60px;
    }
}

@media (max-width: 480px) {
    .restaurant-info {
        padding: 1rem 0;
    }

    .restaurant-name {
        font-size: 1.5rem;
    }

    .product-card {
        margin: 0 0.5rem;
    }

    .category-title {
        font-size: 1.5rem;
    }

    .lang-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }
}
