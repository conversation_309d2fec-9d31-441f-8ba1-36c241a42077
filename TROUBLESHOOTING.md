# دليل حل مشاكل منصة Menuz على الاستضافة المشتركة

## المشكلة الحالية: خطأ إنشاء قاعدة البيانات

### رسالة الخطأ:
```
#1044 - Access denied for user 'cpses_mexvjbfraf'@'localhost' to database 'menuz_db'
```

### السبب:
المستخدم في الاستضافة المشتركة لا يملك صلاحيات إنشاء قواعد بيانات جديدة.

### الحل المطبق:
✅ تم إنشاء ملف `database/shared_hosting_schema.sql` بدون أمر `CREATE DATABASE`
✅ تم تحديث `install.php` للعمل مع قاعدة البيانات الموجودة مسبقاً

## خطوات حل المشكلة:

### 1. التحقق من الاتصال
```bash
# افتح في المتصفح:
https://bag.meedps.com/test_connection.php
```

### 2. تشغيل التثبيت المحدث
```bash
# افتح في المتصفح:
https://bag.meedps.com/install.php
```

### 3. إذا استمرت المشاكل - الحل اليدوي

#### أ) تسجيل الدخول لـ phpMyAdmin
1. ادخل على لوحة تحكم الاستضافة
2. افتح phpMyAdmin
3. اختر قاعدة البيانات `meedpsco_Menu`

#### ب) تنفيذ الاستعلامات يدوياً
انسخ والصق المحتوى من `database/shared_hosting_schema.sql` في phpMyAdmin

#### ج) تنفيذ الاستعلامات خطوة بخطوة:

```sql
-- 1. إنشاء جدول المستخدمين
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    status ENUM('active', 'inactive', 'suspended') DEFAULT 'active',
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_token_expires DATETIME,
    remember_token VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

```sql
-- 2. إنشاء جدول المطاعم
CREATE TABLE IF NOT EXISTS restaurants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(255) NOT NULL,
    name_en VARCHAR(255),
    slug VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    description_en TEXT,
    logo VARCHAR(255),
    cover_image VARCHAR(255),
    phone VARCHAR(20),
    address TEXT,
    address_en TEXT,
    working_hours JSON,
    social_media JSON,
    theme_color VARCHAR(7) DEFAULT '#667eea',
    custom_css TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_slug (slug),
    INDEX idx_user_id (user_id)
);
```

```sql
-- 3. إنشاء جدول الباقات
CREATE TABLE IF NOT EXISTS plans (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    name_ar VARCHAR(50) NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    products_limit INT DEFAULT -1,
    features JSON NOT NULL,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

```sql
-- 4. إدراج البيانات الأساسية للباقات
INSERT IGNORE INTO plans (id, name, name_ar, price, products_limit, features) VALUES
(1, 'basic', 'أساسية', 3.00, 15, '["internal_orders"]'),
(2, 'pro', 'احترافية', 6.00, 50, '["internal_orders", "external_orders", "basic_stats"]'),
(3, 'ultra', 'متقدمة', 8.00, -1, '["internal_orders", "external_orders", "basic_stats", "advanced_stats", "custom_design", "custom_qr", "online_payment"]');
```

## مشاكل شائعة أخرى وحلولها:

### 1. خطأ 500 - Internal Server Error

#### الأسباب:
- خطأ في ملف .htaccess
- صلاحيات ملفات خاطئة
- نفاد الذاكرة

#### الحلول:
```bash
# تعيين صلاحيات صحيحة
chmod 644 .htaccess
chmod 644 *.php
chmod 755 uploads/
chmod 755 config/

# إذا استمر الخطأ، أعد تسمية .htaccess مؤقتاً
mv .htaccess .htaccess_backup
```

### 2. خطأ في رفع الصور

#### التحقق من:
```bash
# صلاحيات مجلد uploads
chmod 755 uploads/

# التأكد من وجود ملف .htaccess في uploads
cat uploads/.htaccess
```

### 3. مشاكل الجلسات

#### الحلول:
```php
// إضافة في بداية config/database.php
ini_set('session.save_path', '/tmp');
ini_set('session.gc_maxlifetime', 1440);
```

### 4. مشاكل الذاكرة

#### الحلول:
```php
// إضافة في config/hosting.php
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);
```

## فحص الحالة:

### 1. اختبار الاتصال
```bash
curl -I https://bag.meedps.com/test_connection.php
```

### 2. فحص الأخطاء
```bash
# عرض سجل الأخطاء
tail -f logs/error.log
tail -f logs/app.log
```

### 3. فحص قاعدة البيانات
```sql
-- في phpMyAdmin
SHOW TABLES;
SELECT COUNT(*) FROM plans;
DESCRIBE users;
```

## معلومات الاتصال بالدعم:

### معلومات الاستضافة:
- **الموقع**: bag.meedps.com
- **قاعدة البيانات**: meedpsco_Menu
- **المستخدم**: meedpsco_Menu

### ملفات مهمة للدعم:
- `test_connection.php` - اختبار الاتصال
- `logs/error.log` - سجل الأخطاء
- `config/database.php` - إعدادات قاعدة البيانات

## خطوات التحقق النهائية:

1. ✅ تشغيل `test_connection.php`
2. ✅ تشغيل `install.php`
3. ✅ إنشاء حساب جديد
4. ✅ إنشاء مطعم تجريبي
5. ✅ إضافة منتج تجريبي
6. ✅ اختبار عرض المنيو

---

**ملاحظة**: احذف ملف `test_connection.php` بعد التأكد من عمل الموقع بشكل صحيح لأسباب أمنية.
