<?php
// بدء الجلسة مع إعدادات أمان محسنة
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);
session_start();

// التحقق من وجود ملفات التكوين
if (!file_exists('config/database.php')) {
    // إعادة توجيه لصفحة التثبيت
    header('Location: install.php');
    exit;
}

require_once 'config/database.php';
require_once 'includes/functions.php';

// تنظيف المدخلات
$restaurant_slug = isset($_GET['restaurant']) ? sanitize_input($_GET['restaurant']) : null;

// التحقق من صحة slug المطعم
if ($restaurant_slug && !preg_match('/^[a-zA-Z0-9\-_]+$/', $restaurant_slug)) {
    header("HTTP/1.0 404 Not Found");
    include 'public/404.php';
    exit;
}

try {
    if ($restaurant_slug) {
        // عرض منيو المطعم
        include 'public/menu.php';
    } else {
        // عرض الصفحة الرئيسية
        include 'public/home.php';
    }
} catch (Exception $e) {
    // تسجيل الخطأ وعرض صفحة خطأ عامة
    error_log("خطأ في index.php: " . $e->getMessage());
    header("HTTP/1.0 500 Internal Server Error");
    include 'public/500.php';
}
?>
