<?php
/**
 * ملف اختبار الاتصال بقاعدة البيانات
 * استخدم هذا الملف للتأكد من صحة إعدادات قاعدة البيانات
 */

// إعدادات قاعدة البيانات
$db_host = 'localhost';
$db_name = 'meedpsco_Menu';
$db_user = 'meedpsco_Menu';
$db_pass = 'meedpsco_Menu';

echo "<h2>اختبار الاتصال بقاعدة البيانات</h2>";
echo "<hr>";

// اختبار الاتصال
try {
    echo "<p>🔄 محاولة الاتصال بقاعدة البيانات...</p>";
    
    $pdo = new PDO(
        "mysql:host={$db_host};dbname={$db_name};charset=utf8mb4",
        $db_user,
        $db_pass,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_TIMEOUT => 10
        ]
    );
    
    echo "<p>✅ <strong>نجح الاتصال بقاعدة البيانات!</strong></p>";
    
    // اختبار استعلام بسيط
    $stmt = $pdo->query("SELECT DATABASE() as db_name, VERSION() as mysql_version, NOW() as current_time");
    $result = $stmt->fetch();
    
    echo "<h3>معلومات قاعدة البيانات:</h3>";
    echo "<ul>";
    echo "<li><strong>اسم قاعدة البيانات:</strong> " . $result['db_name'] . "</li>";
    echo "<li><strong>إصدار MySQL:</strong> " . $result['mysql_version'] . "</li>";
    echo "<li><strong>الوقت الحالي:</strong> " . $result['current_time'] . "</li>";
    echo "</ul>";
    
    // فحص الجداول الموجودة
    echo "<h3>الجداول الموجودة:</h3>";
    $stmt = $pdo->query("SHOW TABLES");
    $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
    
    if (empty($tables)) {
        echo "<p>⚠️ لا توجد جداول في قاعدة البيانات. يجب تشغيل ملف التثبيت.</p>";
        echo "<p><a href='install.php' style='background: #007cba; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>تشغيل التثبيت</a></p>";
    } else {
        echo "<ul>";
        foreach ($tables as $table) {
            echo "<li>✅ {$table}</li>";
        }
        echo "</ul>";
        echo "<p>✅ <strong>تم العثور على " . count($tables) . " جدول</strong></p>";
    }
    
    // فحص صلاحيات المستخدم
    echo "<h3>صلاحيات المستخدم:</h3>";
    try {
        $stmt = $pdo->query("SHOW GRANTS FOR CURRENT_USER()");
        $grants = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "<ul>";
        foreach ($grants as $grant) {
            echo "<li>" . htmlspecialchars($grant) . "</li>";
        }
        echo "</ul>";
    } catch (Exception $e) {
        echo "<p>⚠️ لا يمكن عرض الصلاحيات: " . $e->getMessage() . "</p>";
    }
    
} catch (PDOException $e) {
    echo "<p>❌ <strong>فشل الاتصال بقاعدة البيانات!</strong></p>";
    echo "<p><strong>رسالة الخطأ:</strong> " . $e->getMessage() . "</p>";
    
    // نصائح لحل المشاكل الشائعة
    echo "<h3>نصائح لحل المشكلة:</h3>";
    echo "<ul>";
    echo "<li>تأكد من صحة بيانات الاتصال (اسم المستخدم، كلمة المرور، اسم قاعدة البيانات)</li>";
    echo "<li>تأكد من أن قاعدة البيانات مفعلة في لوحة تحكم الاستضافة</li>";
    echo "<li>تأكد من أن المستخدم له صلاحيات الوصول لقاعدة البيانات</li>";
    echo "<li>تواصل مع الدعم الفني للاستضافة إذا استمرت المشكلة</li>";
    echo "</ul>";
}

// فحص إعدادات PHP المهمة
echo "<hr>";
echo "<h3>إعدادات PHP:</h3>";
echo "<ul>";
echo "<li><strong>إصدار PHP:</strong> " . PHP_VERSION . "</li>";
echo "<li><strong>PDO MySQL:</strong> " . (extension_loaded('pdo_mysql') ? '✅ متوفر' : '❌ غير متوفر') . "</li>";
echo "<li><strong>GD Extension:</strong> " . (extension_loaded('gd') ? '✅ متوفر' : '❌ غير متوفر') . "</li>";
echo "<li><strong>cURL Extension:</strong> " . (extension_loaded('curl') ? '✅ متوفر' : '❌ غير متوفر') . "</li>";
echo "<li><strong>JSON Extension:</strong> " . (extension_loaded('json') ? '✅ متوفر' : '❌ غير متوفر') . "</li>";
echo "<li><strong>mbstring Extension:</strong> " . (extension_loaded('mbstring') ? '✅ متوفر' : '❌ غير متوفر') . "</li>";
echo "<li><strong>Memory Limit:</strong> " . ini_get('memory_limit') . "</li>";
echo "<li><strong>Max Execution Time:</strong> " . ini_get('max_execution_time') . " ثانية</li>";
echo "<li><strong>Upload Max Filesize:</strong> " . ini_get('upload_max_filesize') . "</li>";
echo "<li><strong>Post Max Size:</strong> " . ini_get('post_max_size') . "</li>";
echo "</ul>";

// فحص صلاحيات الملفات
echo "<hr>";
echo "<h3>صلاحيات المجلدات:</h3>";
echo "<ul>";
echo "<li><strong>config/:</strong> " . (is_writable('config') ? '✅ قابل للكتابة' : '❌ غير قابل للكتابة') . "</li>";
echo "<li><strong>uploads/:</strong> " . (is_dir('uploads') ? (is_writable('uploads') ? '✅ قابل للكتابة' : '❌ غير قابل للكتابة') : '⚠️ غير موجود') . "</li>";
echo "<li><strong>المجلد الجذر:</strong> " . (is_writable('.') ? '✅ قابل للكتابة' : '❌ غير قابل للكتابة') . "</li>";
echo "</ul>";

echo "<hr>";
echo "<p><strong>ملاحظة:</strong> احذف هذا الملف بعد التأكد من عمل الموقع بشكل صحيح.</p>";
echo "<p><a href='index.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>الانتقال للموقع</a></p>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 50px auto;
    padding: 20px;
    background: #f5f5f5;
    line-height: 1.6;
}

h2, h3 {
    color: #333;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

li {
    margin-bottom: 8px;
}

p {
    background: white;
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

hr {
    border: none;
    height: 2px;
    background: linear-gradient(to right, #667eea, #764ba2);
    margin: 30px 0;
}
</style>
