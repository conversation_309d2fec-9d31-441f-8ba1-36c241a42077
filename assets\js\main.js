// JavaScript رئيسي للموقع

document.addEventListener('DOMContentLoaded', function() {
    // تهيئة الموقع
    initializeApp();
});

/**
 * تهيئة التطبيق
 */
function initializeApp() {
    // إخفاء التنبيهات تلقائياً
    autoHideAlerts();
    
    // تهيئة النماذج
    initializeForms();
    
    // تهيئة القوائم المنسدلة
    initializeDropdowns();
    
    // تهيئة المودال
    initializeModals();
}

/**
 * إخفاء التنبيهات تلقائياً
 */
function autoHideAlerts() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.remove();
            }, 300);
        }, 5000);
    });
}

/**
 * تهيئة النماذج
 */
function initializeForms() {
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
            }
        });
    });
}

/**
 * التحقق من صحة النماذج
 */
function validateForm(form) {
    let isValid = true;
    const requiredFields = form.querySelectorAll('[required]');
    
    requiredFields.forEach(field => {
        if (!field.value.trim()) {
            showFieldError(field, 'هذا الحقل مطلوب');
            isValid = false;
        } else {
            clearFieldError(field);
        }
    });
    
    // التحقق من البريد الإلكتروني
    const emailFields = form.querySelectorAll('input[type="email"]');
    emailFields.forEach(field => {
        if (field.value && !isValidEmail(field.value)) {
            showFieldError(field, 'البريد الإلكتروني غير صحيح');
            isValid = false;
        }
    });
    
    // التحقق من كلمة المرور
    const passwordFields = form.querySelectorAll('input[type="password"]');
    if (passwordFields.length > 1) {
        const password = passwordFields[0].value;
        const confirmPassword = passwordFields[1].value;
        
        if (password !== confirmPassword) {
            showFieldError(passwordFields[1], 'كلمة المرور غير متطابقة');
            isValid = false;
        }
    }
    
    return isValid;
}

/**
 * عرض خطأ في الحقل
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.style.borderColor = '#f44336';
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.color = '#f44336';
    errorDiv.style.fontSize = '0.875rem';
    errorDiv.style.marginTop = '0.25rem';
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * إزالة خطأ الحقل
 */
function clearFieldError(field) {
    field.style.borderColor = '';
    
    const existingError = field.parentNode.querySelector('.field-error');
    if (existingError) {
        existingError.remove();
    }
}

/**
 * التحقق من صحة البريد الإلكتروني
 */
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

/**
 * تهيئة القوائم المنسدلة
 */
function initializeDropdowns() {
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const toggle = dropdown.querySelector('.dropdown-toggle');
        const menu = dropdown.querySelector('.dropdown-menu');
        
        if (toggle && menu) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                menu.classList.toggle('show');
            });
            
            // إغلاق القائمة عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!dropdown.contains(e.target)) {
                    menu.classList.remove('show');
                }
            });
        }
    });
}

/**
 * تهيئة المودال
 */
function initializeModals() {
    const modalTriggers = document.querySelectorAll('[data-modal]');
    modalTriggers.forEach(trigger => {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            const modalId = this.getAttribute('data-modal');
            const modal = document.getElementById(modalId);
            if (modal) {
                showModal(modal);
            }
        });
    });
    
    // إغلاق المودال
    const closeButtons = document.querySelectorAll('.modal-close');
    closeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const modal = this.closest('.modal');
            if (modal) {
                hideModal(modal);
            }
        });
    });
    
    // إغلاق المودال عند النقر على الخلفية
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                hideModal(this);
            }
        });
    });
}

/**
 * عرض المودال
 */
function showModal(modal) {
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
    
    setTimeout(() => {
        modal.classList.add('show');
    }, 10);
}

/**
 * إخفاء المودال
 */
function hideModal(modal) {
    modal.classList.remove('show');
    
    setTimeout(() => {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }, 300);
}

/**
 * عرض تنبيه
 */
function showAlert(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;
    alertDiv.textContent = message;
    
    const container = document.querySelector('.container') || document.body;
    container.insertBefore(alertDiv, container.firstChild);
    
    // إخفاء التنبيه تلقائياً
    setTimeout(() => {
        alertDiv.style.opacity = '0';
        setTimeout(() => {
            alertDiv.remove();
        }, 300);
    }, 5000);
}

/**
 * تحميل المحتوى بـ AJAX
 */
function loadContent(url, container, callback) {
    fetch(url)
        .then(response => response.text())
        .then(data => {
            if (container) {
                container.innerHTML = data;
            }
            if (callback) {
                callback(data);
            }
        })
        .catch(error => {
            console.error('خطأ في تحميل المحتوى:', error);
            showAlert('حدث خطأ في تحميل المحتوى', 'error');
        });
}

/**
 * إرسال نموذج بـ AJAX
 */
function submitForm(form, callback) {
    const formData = new FormData(form);
    
    fetch(form.action, {
        method: form.method || 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (callback) {
            callback(data);
        }
    })
    .catch(error => {
        console.error('خطأ في إرسال النموذج:', error);
        showAlert('حدث خطأ في إرسال البيانات', 'error');
    });
}

/**
 * تنسيق الأرقام
 */
function formatNumber(number) {
    return new Intl.NumberFormat('ar-SA').format(number);
}

/**
 * تنسيق العملة
 */
function formatCurrency(amount, currency = 'USD') {
    return new Intl.NumberFormat('ar-SA', {
        style: 'currency',
        currency: currency
    }).format(amount);
}

/**
 * نسخ النص إلى الحافظة
 */
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showAlert('تم نسخ النص بنجاح', 'success');
    }).catch(() => {
        showAlert('فشل في نسخ النص', 'error');
    });
}

/**
 * تبديل اللغة
 */
function toggleLanguage() {
    const currentLang = document.documentElement.lang || 'ar';
    const newLang = currentLang === 'ar' ? 'en' : 'ar';
    
    // تحديث اتجاه الصفحة
    if (newLang === 'ar') {
        document.documentElement.dir = 'rtl';
        document.body.style.textAlign = 'right';
    } else {
        document.documentElement.dir = 'ltr';
        document.body.style.textAlign = 'left';
    }
    
    document.documentElement.lang = newLang;
    
    // حفظ اللغة في التخزين المحلي
    localStorage.setItem('language', newLang);
    
    // إعادة تحميل الصفحة لتطبيق اللغة الجديدة
    location.reload();
}

// تحميل اللغة المحفوظة
document.addEventListener('DOMContentLoaded', function() {
    const savedLang = localStorage.getItem('language');
    if (savedLang && savedLang !== document.documentElement.lang) {
        toggleLanguage();
    }
});
