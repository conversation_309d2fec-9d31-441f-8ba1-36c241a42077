<?php
// الحصول على معلومات المطعم
$restaurant_slug = $_GET['restaurant'] ?? '';

if (empty($restaurant_slug)) {
    header("HTTP/1.0 404 Not Found");
    include '404.php';
    exit;
}

try {
    // الحصول على معلومات المطعم
    $stmt = $pdo->prepare("
        SELECT r.*, u.first_name, u.last_name, u.phone as owner_phone,
               s.plan_id, s.status as subscription_status,
               p.features
        FROM restaurants r
        JOIN users u ON r.user_id = u.id
        LEFT JOIN subscriptions s ON u.id = s.user_id AND s.status = 'active'
        LEFT JOIN plans p ON s.plan_id = p.id
        WHERE r.slug = ? AND r.status = 'active'
    ");
    $stmt->execute([$restaurant_slug]);
    $restaurant = $stmt->fetch();
    
    if (!$restaurant) {
        header("HTTP/1.0 404 Not Found");
        include '404.php';
        exit;
    }
    
    // التحقق من حالة الاشتراك
    if ($restaurant['subscription_status'] !== 'active') {
        include 'restaurant-suspended.php';
        exit;
    }
    
    // الحصول على الفئات والمنتجات
    $stmt = $pdo->prepare("
        SELECT * FROM categories 
        WHERE restaurant_id = ? AND status = 'active' 
        ORDER BY sort_order ASC, name ASC
    ");
    $stmt->execute([$restaurant['id']]);
    $categories = $stmt->fetchAll();
    
    $products_by_category = [];
    foreach ($categories as $category) {
        $stmt = $pdo->prepare("
            SELECT * FROM products 
            WHERE restaurant_id = ? AND category_id = ? AND status = 'active' AND is_available = 1
            ORDER BY sort_order ASC, name ASC
        ");
        $stmt->execute([$restaurant['id'], $category['id']]);
        $products_by_category[$category['id']] = $stmt->fetchAll();
    }
    
    // تسجيل الزيارة
    $today = date('Y-m-d');
    $stmt = $pdo->prepare("
        INSERT INTO analytics (restaurant_id, date, page_views, unique_visitors) 
        VALUES (?, ?, 1, 1)
        ON DUPLICATE KEY UPDATE 
        page_views = page_views + 1,
        unique_visitors = unique_visitors + 1
    ");
    $stmt->execute([$restaurant['id'], $today]);
    
    // اللغة الحالية
    $current_lang = $_GET['lang'] ?? 'ar';
    
} catch (PDOException $e) {
    header("HTTP/1.0 500 Internal Server Error");
    include '500.php';
    exit;
}
?>

<!DOCTYPE html>
<html lang="<?php echo $current_lang; ?>" dir="<?php echo $current_lang === 'ar' ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($current_lang === 'ar' ? $restaurant['name'] : ($restaurant['name_en'] ?: $restaurant['name'])); ?> - منيو رقمية</title>
    <meta name="description" content="<?php echo htmlspecialchars($current_lang === 'ar' ? $restaurant['description'] : ($restaurant['description_en'] ?: $restaurant['description'])); ?>">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo htmlspecialchars($restaurant['name']); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($restaurant['description']); ?>">
    <meta property="og:type" content="restaurant">
    <meta property="og:url" content="<?php echo SITE_URL . '/?restaurant=' . $restaurant['slug']; ?>">
    <?php if ($restaurant['cover_image']): ?>
        <meta property="og:image" content="<?php echo SITE_URL . '/' . UPLOAD_PATH . $restaurant['cover_image']; ?>">
    <?php endif; ?>
    
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/menu.css">
    
    <style>
        :root {
            --restaurant-primary: <?php echo $restaurant['theme_color'] ?: '#667eea'; ?>;
        }
    </style>
    
    <?php if ($restaurant['custom_css']): ?>
        <style><?php echo $restaurant['custom_css']; ?></style>
    <?php endif; ?>
</head>
<body class="menu-page">
    <!-- هيدر المطعم -->
    <header class="restaurant-header">
        <?php if ($restaurant['cover_image']): ?>
            <div class="cover-image">
                <img src="<?php echo UPLOAD_PATH . $restaurant['cover_image']; ?>" alt="<?php echo htmlspecialchars($restaurant['name']); ?>">
            </div>
        <?php endif; ?>
        
        <div class="restaurant-info">
            <div class="container">
                <div class="restaurant-details">
                    <?php if ($restaurant['logo']): ?>
                        <div class="restaurant-logo">
                            <img src="<?php echo UPLOAD_PATH . $restaurant['logo']; ?>" alt="<?php echo htmlspecialchars($restaurant['name']); ?>">
                        </div>
                    <?php endif; ?>
                    
                    <div class="restaurant-text">
                        <h1 class="restaurant-name">
                            <?php echo htmlspecialchars($current_lang === 'ar' ? $restaurant['name'] : ($restaurant['name_en'] ?: $restaurant['name'])); ?>
                        </h1>
                        
                        <?php if ($restaurant['description'] || $restaurant['description_en']): ?>
                            <p class="restaurant-description">
                                <?php echo htmlspecialchars($current_lang === 'ar' ? $restaurant['description'] : ($restaurant['description_en'] ?: $restaurant['description'])); ?>
                            </p>
                        <?php endif; ?>
                        
                        <div class="restaurant-meta">
                            <?php if ($restaurant['phone']): ?>
                                <div class="meta-item">
                                    <span class="meta-icon">📞</span>
                                    <a href="tel:<?php echo $restaurant['phone']; ?>"><?php echo htmlspecialchars($restaurant['phone']); ?></a>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($restaurant['address'] || $restaurant['address_en']): ?>
                                <div class="meta-item">
                                    <span class="meta-icon">📍</span>
                                    <span><?php echo htmlspecialchars($current_lang === 'ar' ? $restaurant['address'] : ($restaurant['address_en'] ?: $restaurant['address'])); ?></span>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="header-actions">
                    <!-- تبديل اللغة -->
                    <div class="language-switcher">
                        <a href="?restaurant=<?php echo $restaurant['slug']; ?>&lang=ar" 
                           class="lang-btn <?php echo $current_lang === 'ar' ? 'active' : ''; ?>">عربي</a>
                        <a href="?restaurant=<?php echo $restaurant['slug']; ?>&lang=en" 
                           class="lang-btn <?php echo $current_lang === 'en' ? 'active' : ''; ?>">English</a>
                    </div>
                    
                    <!-- سلة الطلبات -->
                    <button class="cart-btn" onclick="toggleCart()">
                        <span class="cart-icon">🛒</span>
                        <span class="cart-count">0</span>
                    </button>
                </div>
            </div>
        </div>
    </header>
    
    <!-- قائمة الفئات -->
    <?php if (count($categories) > 1): ?>
        <nav class="categories-nav">
            <div class="container">
                <div class="categories-list">
                    <?php foreach ($categories as $category): ?>
                        <a href="#category-<?php echo $category['id']; ?>" class="category-link">
                            <?php echo htmlspecialchars($current_lang === 'ar' ? $category['name'] : ($category['name_en'] ?: $category['name'])); ?>
                        </a>
                    <?php endforeach; ?>
                </div>
            </div>
        </nav>
    <?php endif; ?>
    
    <!-- المنتجات -->
    <main class="menu-content">
        <div class="container">
            <?php foreach ($categories as $category): ?>
                <?php if (!empty($products_by_category[$category['id']])): ?>
                    <section id="category-<?php echo $category['id']; ?>" class="category-section">
                        <div class="category-header">
                            <h2 class="category-title">
                                <?php echo htmlspecialchars($current_lang === 'ar' ? $category['name'] : ($category['name_en'] ?: $category['name'])); ?>
                            </h2>
                            
                            <?php if ($category['description'] || $category['description_en']): ?>
                                <p class="category-description">
                                    <?php echo htmlspecialchars($current_lang === 'ar' ? $category['description'] : ($category['description_en'] ?: $category['description'])); ?>
                                </p>
                            <?php endif; ?>
                        </div>
                        
                        <div class="products-grid">
                            <?php foreach ($products_by_category[$category['id']] as $product): ?>
                                <div class="product-card" data-product-id="<?php echo $product['id']; ?>">
                                    <?php if ($product['image']): ?>
                                        <div class="product-image">
                                            <img src="<?php echo UPLOAD_PATH . $product['image']; ?>" 
                                                 alt="<?php echo htmlspecialchars($product['name']); ?>"
                                                 loading="lazy">
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="product-info">
                                        <h3 class="product-name">
                                            <?php echo htmlspecialchars($current_lang === 'ar' ? $product['name'] : ($product['name_en'] ?: $product['name'])); ?>
                                        </h3>
                                        
                                        <?php if ($product['description'] || $product['description_en']): ?>
                                            <p class="product-description">
                                                <?php echo htmlspecialchars($current_lang === 'ar' ? $product['description'] : ($product['description_en'] ?: $product['description'])); ?>
                                            </p>
                                        <?php endif; ?>
                                        
                                        <?php if ($product['ingredients'] || $product['ingredients_en']): ?>
                                            <p class="product-ingredients">
                                                <strong><?php echo $current_lang === 'ar' ? 'المكونات:' : 'Ingredients:'; ?></strong>
                                                <?php echo htmlspecialchars($current_lang === 'ar' ? $product['ingredients'] : ($product['ingredients_en'] ?: $product['ingredients'])); ?>
                                            </p>
                                        <?php endif; ?>
                                        
                                        <div class="product-meta">
                                            <div class="product-price">
                                                $<?php echo number_format($product['price'], 2); ?>
                                            </div>
                                            
                                            <?php if ($product['calories']): ?>
                                                <div class="product-calories">
                                                    <?php echo $product['calories']; ?> <?php echo $current_lang === 'ar' ? 'سعرة' : 'cal'; ?>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if ($product['preparation_time']): ?>
                                                <div class="product-time">
                                                    ⏱️ <?php echo $product['preparation_time']; ?> <?php echo $current_lang === 'ar' ? 'دقيقة' : 'min'; ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        
                                        <button class="add-to-cart-btn" onclick="addToCart(<?php echo $product['id']; ?>)">
                                            <span><?php echo $current_lang === 'ar' ? 'إضافة للطلب' : 'Add to Order'; ?></span>
                                        </button>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </section>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
    </main>
    
    <!-- سلة الطلبات -->
    <div id="cart-sidebar" class="cart-sidebar">
        <div class="cart-header">
            <h3><?php echo $current_lang === 'ar' ? 'طلبك' : 'Your Order'; ?></h3>
            <button class="cart-close" onclick="toggleCart()">×</button>
        </div>
        
        <div class="cart-items" id="cart-items">
            <div class="empty-cart">
                <p><?php echo $current_lang === 'ar' ? 'لا توجد منتجات في طلبك' : 'No items in your order'; ?></p>
            </div>
        </div>
        
        <div class="cart-footer">
            <div class="cart-total">
                <strong><?php echo $current_lang === 'ar' ? 'المجموع:' : 'Total:'; ?> $<span id="cart-total">0.00</span></strong>
            </div>
            <button class="checkout-btn" onclick="proceedToCheckout()" disabled>
                <?php echo $current_lang === 'ar' ? 'تأكيد الطلب' : 'Proceed to Checkout'; ?>
            </button>
        </div>
    </div>
    
    <!-- خلفية السلة -->
    <div id="cart-overlay" class="cart-overlay" onclick="toggleCart()"></div>
    
    <!-- فوتر -->
    <footer class="menu-footer">
        <div class="container">
            <p><?php echo $current_lang === 'ar' ? 'مدعوم بواسطة' : 'Powered by'; ?> <a href="<?php echo SITE_URL; ?>" target="_blank"><?php echo SITE_NAME; ?></a></p>
        </div>
    </footer>
    
    <script src="assets/js/main.js"></script>
    <script src="assets/js/menu.js"></script>
    
    <script>
        // بيانات المطعم والمنتجات
        const restaurantData = {
            id: <?php echo $restaurant['id']; ?>,
            slug: '<?php echo $restaurant['slug']; ?>',
            name: '<?php echo htmlspecialchars($restaurant['name']); ?>',
            features: <?php echo json_encode(json_decode($restaurant['features'] ?: '[]')); ?>,
            lang: '<?php echo $current_lang; ?>'
        };
        
        const productsData = <?php 
            $all_products = [];
            foreach ($products_by_category as $products) {
                foreach ($products as $product) {
                    $all_products[$product['id']] = [
                        'id' => $product['id'],
                        'name' => $current_lang === 'ar' ? $product['name'] : ($product['name_en'] ?: $product['name']),
                        'price' => (float)$product['price'],
                        'image' => $product['image']
                    ];
                }
            }
            echo json_encode($all_products);
        ?>;
    </script>
</body>
</html>
