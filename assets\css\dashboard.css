/* تنسيقات لوحة التحكم */

body {
    background: #f8f9fa;
    margin: 0;
    padding: 0;
}

/* تخطيط لوحة التحكم */
.dashboard-layout {
    display: flex;
    min-height: 100vh;
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: white;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    transition: transform 0.3s ease;
}

.sidebar-header {
    padding: 2rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.sidebar-logo {
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 1rem 1.5rem;
    color: var(--text-dark);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: var(--primary-color);
    border-right-color: var(--primary-color);
}

.nav-link.active {
    background: rgba(102, 126, 234, 0.15);
    color: var(--primary-color);
    border-right-color: var(--primary-color);
    font-weight: 600;
}

.nav-icon {
    margin-left: 0.75rem;
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* هيدر لوحة التحكم */
.dashboard-header {
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 999;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: var(--text-dark);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-menu {
    position: relative;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    cursor: pointer;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 8px;
    box-shadow: var(--shadow-hover);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: 0.75rem 1rem;
    color: var(--text-dark);
    text-decoration: none;
    transition: background 0.3s ease;
}

.dropdown-item:hover {
    background: var(--bg-light);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-color);
    margin: 0.5rem 0;
}

/* المحتوى */
.content {
    flex: 1;
    padding: 2rem;
}

/* قسم الترحيب */
.welcome-section {
    margin-bottom: 2rem;
}

.welcome-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
}

.welcome-section p {
    color: var(--text-light);
    font-size: 1.1rem;
}

/* بطاقة الإعداد */
.setup-card {
    margin-bottom: 2rem;
}

.setup-card .card {
    border: 2px dashed var(--border-color);
    background: rgba(102, 126, 234, 0.05);
}

/* شبكة الإحصائيات */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--shadow);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-hover);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-dark);
    line-height: 1;
}

.stat-label {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-top: 0.25rem;
}

/* الإجراءات السريعة */
.quick-actions {
    margin-bottom: 2rem;
}

.quick-actions h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.action-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-decoration: none;
    color: var(--text-dark);
    box-shadow: var(--shadow);
    transition: all 0.3s ease;
    text-align: center;
}

.action-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-hover);
    color: var(--primary-color);
}

.action-icon {
    font-size: 2rem;
    margin-bottom: 1rem;
}

.action-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.action-desc {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* الطلبات الحديثة */
.recent-orders {
    margin-bottom: 2rem;
}

.recent-orders h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

/* معلومات المطعم */
.restaurant-info h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: var(--text-dark);
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-color);
}

.info-item:last-child {
    border-bottom: none;
}

.info-item strong {
    color: var(--text-dark);
}

.info-item span {
    color: var(--text-light);
}

/* حالات الطلبات */
.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.status-pending {
    background: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.status-confirmed {
    background: rgba(33, 150, 243, 0.1);
    color: #2196f3;
}

.status-preparing {
    background: rgba(156, 39, 176, 0.1);
    color: #9c27b0;
}

.status-ready {
    background: rgba(76, 175, 80, 0.1);
    color: var(--success-color);
}

.status-delivered {
    background: rgba(76, 175, 80, 0.2);
    color: var(--success-color);
}

.status-cancelled {
    background: rgba(244, 67, 54, 0.1);
    color: var(--error-color);
}

/* الجداول المتجاوبة */
.table-responsive {
    overflow-x: auto;
}

/* الاستجابة للأجهزة المحمولة */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: 100%;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .content {
        padding: 1rem;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .actions-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }
    
    .info-grid {
        grid-template-columns: 1fr;
    }
    
    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .dashboard-header {
        padding: 1rem;
    }
    
    .welcome-section h1 {
        font-size: 2rem;
    }
}

@media (max-width: 480px) {
    .welcome-section h1 {
        font-size: 1.5rem;
    }
    
    .stat-card {
        flex-direction: column;
        text-align: center;
    }
    
    .action-card {
        padding: 1rem;
    }
}
