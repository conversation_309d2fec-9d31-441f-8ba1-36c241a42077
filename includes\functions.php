<?php
// دوال مساعدة عامة

/**
 * تنظيف البيانات المدخلة
 */
function sanitize_input($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

/**
 * التحقق من تسجيل الدخول
 */
function is_logged_in() {
    return isset($_SESSION['user_id']);
}

/**
 * إعادة التوجيه
 */
function redirect($url) {
    header("Location: " . $url);
    exit();
}

/**
 * عرض رسالة تنبيه
 */
function show_alert($message, $type = 'info') {
    return "<div class='alert alert-{$type}'>{$message}</div>";
}

/**
 * تحويل التاريخ إلى تنسيق عربي
 */
function format_date_arabic($date) {
    $months = [
        1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
        5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
        9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
    ];
    
    $timestamp = strtotime($date);
    $day = date('d', $timestamp);
    $month = $months[date('n', $timestamp)];
    $year = date('Y', $timestamp);
    
    return "{$day} {$month} {$year}";
}

/**
 * رفع الملفات
 */
function upload_file($file, $allowed_types = ['jpg', 'jpeg', 'png', 'gif']) {
    if (!isset($file['tmp_name']) || empty($file['tmp_name'])) {
        return ['success' => false, 'message' => 'لم يتم اختيار ملف'];
    }
    
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_types)) {
        return ['success' => false, 'message' => 'نوع الملف غير مدعوم'];
    }
    
    if ($file['size'] > MAX_FILE_SIZE) {
        return ['success' => false, 'message' => 'حجم الملف كبير جداً'];
    }
    
    $new_filename = uniqid() . '.' . $file_extension;
    $upload_path = UPLOAD_PATH . $new_filename;
    
    if (!file_exists(UPLOAD_PATH)) {
        mkdir(UPLOAD_PATH, 0777, true);
    }
    
    if (move_uploaded_file($file['tmp_name'], $upload_path)) {
        return ['success' => true, 'filename' => $new_filename, 'path' => $upload_path];
    } else {
        return ['success' => false, 'message' => 'فشل في رفع الملف'];
    }
}

/**
 * الحصول على معلومات الباقة
 */
function get_plan_info($plan_type) {
    $plans = [
        'basic' => [
            'name' => 'Basic',
            'name_ar' => 'أساسية',
            'price' => BASIC_PRICE,
            'products_limit' => BASIC_PRODUCTS_LIMIT,
            'features' => [
                'منيو واحدة',
                '15 منتج',
                'QR Code عادي',
                'دعم اللغتين',
                'طلبات داخلية فقط'
            ]
        ],
        'pro' => [
            'name' => 'Pro',
            'name_ar' => 'احترافية',
            'price' => PRO_PRICE,
            'products_limit' => PRO_PRODUCTS_LIMIT,
            'features' => [
                'منيو واحدة',
                '50 منتج',
                'QR Code عادي',
                'دعم اللغتين',
                'طلبات داخلية وخارجية',
                'إحصائيات أساسية'
            ]
        ],
        'ultra' => [
            'name' => 'Ultra',
            'name_ar' => 'متقدمة',
            'price' => ULTRA_PRICE,
            'products_limit' => ULTRA_PRODUCTS_LIMIT,
            'features' => [
                'منيو واحدة',
                'منتجات غير محدودة',
                'QR Code مخصص',
                'تصميم مخصص',
                'دعم اللغتين',
                'طلبات داخلية وخارجية',
                'دفع إلكتروني',
                'إحصائيات متقدمة',
                'دعم VIP'
            ]
        ]
    ];
    
    return isset($plans[$plan_type]) ? $plans[$plan_type] : null;
}

/**
 * التحقق من صلاحيات الباقة
 */
function check_plan_permission($user_plan, $required_feature) {
    $permissions = [
        'basic' => ['internal_orders'],
        'pro' => ['internal_orders', 'external_orders', 'basic_stats'],
        'ultra' => ['internal_orders', 'external_orders', 'basic_stats', 'advanced_stats', 'custom_design', 'custom_qr', 'online_payment']
    ];
    
    return isset($permissions[$user_plan]) && in_array($required_feature, $permissions[$user_plan]);
}
?>
