<?php
/**
 * ملف التثبيت السريع لمنصة Menuz
 * يقوم بإنشاء قاعدة البيانات والجداول المطلوبة
 */

// إعدادات قاعدة البيانات للاستضافة المشتركة
$db_config = [
    'host' => 'localhost',
    'username' => 'meedpsco_Menu',
    'password' => 'meedpsco_Menu',
    'database' => 'meedpsco_Menu'
];

$errors = [];
$success = [];

// التحقق من إرسال النموذج
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $db_config['host'] = $_POST['db_host'] ?? 'localhost';
    $db_config['username'] = $_POST['db_username'] ?? 'root';
    $db_config['password'] = $_POST['db_password'] ?? '';
    $db_config['database'] = $_POST['db_name'] ?? 'menuz_db';
    
    try {
        // الاتصال مباشرة بقاعدة البيانات المحددة (للاستضافة المشتركة)
        $pdo = new PDO(
            "mysql:host={$db_config['host']};dbname={$db_config['database']};charset=utf8mb4",
            $db_config['username'],
            $db_config['password']
        );
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        $success[] = "تم الاتصال بقاعدة البيانات بنجاح";
        
        // قراءة ملف SQL الخاص بالاستضافة المشتركة
        $sql_file = 'database/shared_hosting_schema.sql';
        if (file_exists($sql_file)) {
            $sql = file_get_contents($sql_file);

            // تقسيم الاستعلامات وتنظيفها
            $queries = array_filter(array_map('trim', explode(';', $sql)));

            $executed_queries = 0;
            foreach ($queries as $query) {
                // تجاهل التعليقات والاستعلامات الفارغة
                if (!empty($query) &&
                    !preg_match('/^(--|CREATE DATABASE|USE|\s*$)/i', $query) &&
                    !preg_match('/^\/\*.*\*\/$/s', $query)) {

                    try {
                        $pdo->exec($query);
                        $executed_queries++;
                    } catch (PDOException $e) {
                        // تجاهل أخطاء الجداول الموجودة مسبقاً
                        if (strpos($e->getMessage(), 'already exists') === false) {
                            throw $e;
                        }
                    }
                }
            }

            $success[] = "تم إنشاء/تحديث {$executed_queries} جدول بنجاح";
        } else {
            $errors[] = "ملف قاعدة البيانات غير موجود: $sql_file";
        }
        
        // تحديث ملف التكوين
        $config_content = "<?php
// إعدادات قاعدة البيانات للاستضافة المشتركة
define('DB_HOST', '{$db_config['host']}');
define('DB_NAME', '{$db_config['database']}');
define('DB_USER', '{$db_config['username']}');
define('DB_PASS', '{$db_config['password']}');

try {
    \$pdo = new PDO(\"mysql:host=\" . DB_HOST . \";dbname=\" . DB_NAME . \";charset=utf8mb4\", DB_USER, DB_PASS);
    \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    \$pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    \$pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
} catch(PDOException \$e) {
    die(\"خطأ في الاتصال بقاعدة البيانات: \" . \$e->getMessage());
}

// إعدادات عامة للموقع
define('SITE_URL', 'https://bag.meedps.com');
define('SITE_NAME', 'Menuz');
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// إعدادات الباقات
define('BASIC_PRICE', 3);
define('PRO_PRICE', 6);
define('ULTRA_PRICE', 8);

define('BASIC_PRODUCTS_LIMIT', 15);
define('PRO_PRODUCTS_LIMIT', 50);
define('ULTRA_PRODUCTS_LIMIT', -1); // غير محدود
?>";
        
        if (file_put_contents('config/database.php', $config_content)) {
            $success[] = "تم تحديث ملف التكوين بنجاح";
        } else {
            $errors[] = "فشل في تحديث ملف التكوين";
        }
        
        // إنشاء مجلد الرفع
        if (!is_dir('uploads')) {
            if (mkdir('uploads', 0755, true)) {
                $success[] = "تم إنشاء مجلد الرفع بنجاح";
            } else {
                $errors[] = "فشل في إنشاء مجلد الرفع";
            }
        }
        
        // إنشاء ملف .htaccess لمجلد الرفع
        $htaccess_upload = "# منع تنفيذ ملفات PHP في مجلد الرفع
<Files \"*.php\">
    Order Deny,Allow
    Deny from all
</Files>

# السماح بالصور فقط
<FilesMatch \"\\.(jpg|jpeg|png|gif|svg|webp)$\">
    Order Allow,Deny
    Allow from all
</FilesMatch>";
        
        file_put_contents('uploads/.htaccess', $htaccess_upload);
        
        if (empty($errors)) {
            $success[] = "تم تثبيت منصة Menuz بنجاح! يمكنك الآن البدء في استخدام المنصة.";
        }
        
    } catch (PDOException $e) {
        $errors[] = "خطأ في قاعدة البيانات: " . $e->getMessage();
    } catch (Exception $e) {
        $errors[] = "خطأ عام: " . $e->getMessage();
    }
}

// التحقق من متطلبات النظام للاستضافة المشتركة
$requirements = [
    'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
    'PDO Extension' => extension_loaded('pdo'),
    'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
    'GD Extension' => extension_loaded('gd'),
    'cURL Extension' => extension_loaded('curl'),
    'JSON Extension' => extension_loaded('json'),
    'mbstring Extension' => extension_loaded('mbstring'),
    'OpenSSL Extension' => extension_loaded('openssl'),
    'Config Directory Writable' => is_writable('config'),
    'Uploads Directory Writable' => is_writable('.'), // للتحقق من إمكانية إنشاء مجلد uploads
    'HTTPS Available' => (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && $_SERVER['HTTP_X_FORWARDED_PROTO'] === 'https'),
    'Memory Limit >= 128M' => (int)ini_get('memory_limit') >= 128 || ini_get('memory_limit') == -1,
    'Max Execution Time >= 30s' => (int)ini_get('max_execution_time') >= 30 || ini_get('max_execution_time') == 0
];
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تثبيت منصة Menuz</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .install-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 600px;
        }
        
        .install-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .logo {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }
        
        .subtitle {
            color: #666;
            font-size: 1.1rem;
        }
        
        .requirements {
            margin-bottom: 2rem;
        }
        
        .requirement {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.75rem;
            border-bottom: 1px solid #eee;
        }
        
        .requirement:last-child {
            border-bottom: none;
        }
        
        .status {
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .status.pass {
            background: #d4edda;
            color: #155724;
        }
        
        .status.fail {
            background: #f8d7da;
            color: #721c24;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 600;
            color: #333;
        }
        
        .form-control {
            width: 100%;
            padding: 0.75rem;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-control:focus {
            outline: none;
            border-color: #667eea;
        }
        
        .btn {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 1rem 2rem;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.3s ease;
            width: 100%;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .alert {
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .section {
            margin-bottom: 2rem;
        }
        
        .section-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <div class="install-header">
            <div class="logo">Menuz</div>
            <div class="subtitle">تثبيت منصة المنيوهات الرقمية</div>
        </div>
        
        <!-- متطلبات النظام -->
        <div class="section">
            <div class="section-title">متطلبات النظام</div>
            <div class="requirements">
                <?php foreach ($requirements as $requirement => $status): ?>
                    <div class="requirement">
                        <span><?php echo $requirement; ?></span>
                        <span class="status <?php echo $status ? 'pass' : 'fail'; ?>">
                            <?php echo $status ? '✓ متوفر' : '✗ غير متوفر'; ?>
                        </span>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- الرسائل -->
        <?php foreach ($errors as $error): ?>
            <div class="alert alert-error"><?php echo htmlspecialchars($error); ?></div>
        <?php endforeach; ?>
        
        <?php foreach ($success as $message): ?>
            <div class="alert alert-success"><?php echo htmlspecialchars($message); ?></div>
        <?php endforeach; ?>
        
        <!-- نموذج التثبيت -->
        <?php if (empty($success) || !empty($errors)): ?>
            <div class="section">
                <div class="section-title">إعدادات قاعدة البيانات</div>
                
                <form method="POST" action="">
                    <div class="form-group">
                        <label for="db_host" class="form-label">خادم قاعدة البيانات</label>
                        <input type="text" id="db_host" name="db_host" class="form-control" 
                               value="<?php echo htmlspecialchars($db_config['host']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="db_username" class="form-label">اسم المستخدم</label>
                        <input type="text" id="db_username" name="db_username" class="form-control" 
                               value="<?php echo htmlspecialchars($db_config['username']); ?>" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="db_password" class="form-label">كلمة المرور</label>
                        <input type="password" id="db_password" name="db_password" class="form-control" 
                               value="<?php echo htmlspecialchars($db_config['password']); ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="db_name" class="form-label">اسم قاعدة البيانات</label>
                        <input type="text" id="db_name" name="db_name" class="form-control" 
                               value="<?php echo htmlspecialchars($db_config['database']); ?>" required>
                    </div>
                    
                    <button type="submit" class="btn" <?php echo !array_product($requirements) ? 'disabled' : ''; ?>>
                        تثبيت المنصة
                    </button>
                </form>
            </div>
        <?php else: ?>
            <div class="section">
                <div style="text-align: center;">
                    <h2 style="color: #155724; margin-bottom: 1rem;">🎉 تم التثبيت بنجاح!</h2>
                    <p style="margin-bottom: 2rem;">يمكنك الآن البدء في استخدام منصة Menuz</p>
                    <a href="index.php" class="btn" style="display: inline-block; text-decoration: none;">
                        الانتقال للموقع
                    </a>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if (!array_product($requirements)): ?>
            <div class="alert alert-error">
                <strong>تنبيه:</strong> يجب توفر جميع متطلبات النظام قبل المتابعة.
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
