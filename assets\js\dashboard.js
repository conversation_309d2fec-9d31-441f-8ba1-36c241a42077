// JavaScript خاص بلوحة التحكم

document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

/**
 * تهيئة لوحة التحكم
 */
function initializeDashboard() {
    // تهيئة القائمة الجانبية
    initializeSidebar();
    
    // تهيئة قائمة المستخدم
    initializeUserMenu();
    
    // تهيئة الجداول
    initializeTables();
    
    // تهيئة المخططات
    initializeCharts();
    
    // تحديث الإحصائيات
    updateStats();
}

/**
 * تهيئة القائمة الجانبية
 */
function initializeSidebar() {
    const menuToggle = document.querySelector('.menu-toggle');
    const sidebar = document.querySelector('.sidebar');
    const overlay = document.querySelector('.sidebar-overlay');
    
    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
            
            // إنشاء overlay للأجهزة المحمولة
            if (window.innerWidth <= 768) {
                if (sidebar.classList.contains('show')) {
                    createOverlay();
                } else {
                    removeOverlay();
                }
            }
        });
    }
    
    // إغلاق القائمة عند النقر خارجها
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 768 && sidebar && sidebar.classList.contains('show')) {
            if (!sidebar.contains(e.target) && !menuToggle.contains(e.target)) {
                sidebar.classList.remove('show');
                removeOverlay();
            }
        }
    });
    
    // تحديد الرابط النشط
    setActiveNavLink();
}

/**
 * إنشاء overlay للقائمة الجانبية
 */
function createOverlay() {
    if (!document.querySelector('.sidebar-overlay')) {
        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 999;
        `;
        
        overlay.addEventListener('click', function() {
            document.querySelector('.sidebar').classList.remove('show');
            removeOverlay();
        });
        
        document.body.appendChild(overlay);
    }
}

/**
 * إزالة overlay
 */
function removeOverlay() {
    const overlay = document.querySelector('.sidebar-overlay');
    if (overlay) {
        overlay.remove();
    }
}

/**
 * تحديد الرابط النشط في القائمة
 */
function setActiveNavLink() {
    const currentPath = window.location.pathname;
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        const linkPath = new URL(link.href).pathname;
        if (currentPath.includes(linkPath) || (linkPath === '/dashboard/' && currentPath.endsWith('/dashboard/'))) {
            link.classList.add('active');
        } else {
            link.classList.remove('active');
        }
    });
}

/**
 * تهيئة قائمة المستخدم
 */
function initializeUserMenu() {
    const userAvatar = document.querySelector('.user-avatar');
    const userDropdown = document.querySelector('.user-dropdown');
    
    if (userAvatar && userDropdown) {
        userAvatar.addEventListener('click', function(e) {
            e.stopPropagation();
            userDropdown.classList.toggle('show');
        });
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!userAvatar.contains(e.target) && !userDropdown.contains(e.target)) {
                userDropdown.classList.remove('show');
            }
        });
    }
}

/**
 * تهيئة الجداول
 */
function initializeTables() {
    // إضافة فئات CSS للجداول
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        if (!table.classList.contains('table')) {
            table.classList.add('table');
        }
        
        // جعل الجداول متجاوبة
        if (!table.parentElement.classList.contains('table-responsive')) {
            const wrapper = document.createElement('div');
            wrapper.className = 'table-responsive';
            table.parentNode.insertBefore(wrapper, table);
            wrapper.appendChild(table);
        }
    });
}

/**
 * تهيئة المخططات
 */
function initializeCharts() {
    // يمكن إضافة مكتبة مخططات مثل Chart.js هنا
    const chartContainers = document.querySelectorAll('.chart-container');
    
    chartContainers.forEach(container => {
        // إنشاء مخطط بسيط
        createSimpleChart(container);
    });
}

/**
 * إنشاء مخطط بسيط
 */
function createSimpleChart(container) {
    // مخطط بسيط بدون مكتبات خارجية
    const data = JSON.parse(container.dataset.chart || '[]');
    
    if (data.length > 0) {
        const canvas = document.createElement('canvas');
        canvas.width = container.offsetWidth;
        canvas.height = 200;
        
        const ctx = canvas.getContext('2d');
        
        // رسم مخطط خطي بسيط
        drawLineChart(ctx, data, canvas.width, canvas.height);
        
        container.appendChild(canvas);
    }
}

/**
 * رسم مخطط خطي
 */
function drawLineChart(ctx, data, width, height) {
    const padding = 40;
    const chartWidth = width - 2 * padding;
    const chartHeight = height - 2 * padding;
    
    // العثور على القيم الدنيا والعليا
    const values = data.map(item => item.value);
    const minValue = Math.min(...values);
    const maxValue = Math.max(...values);
    const valueRange = maxValue - minValue || 1;
    
    // رسم المحاور
    ctx.strokeStyle = '#e0e0e0';
    ctx.lineWidth = 1;
    
    // المحور الأفقي
    ctx.beginPath();
    ctx.moveTo(padding, height - padding);
    ctx.lineTo(width - padding, height - padding);
    ctx.stroke();
    
    // المحور العمودي
    ctx.beginPath();
    ctx.moveTo(padding, padding);
    ctx.lineTo(padding, height - padding);
    ctx.stroke();
    
    // رسم الخط
    ctx.strokeStyle = '#667eea';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth;
        const y = height - padding - ((item.value - minValue) / valueRange) * chartHeight;
        
        if (index === 0) {
            ctx.moveTo(x, y);
        } else {
            ctx.lineTo(x, y);
        }
    });
    
    ctx.stroke();
    
    // رسم النقاط
    ctx.fillStyle = '#667eea';
    data.forEach((item, index) => {
        const x = padding + (index / (data.length - 1)) * chartWidth;
        const y = height - padding - ((item.value - minValue) / valueRange) * chartHeight;
        
        ctx.beginPath();
        ctx.arc(x, y, 4, 0, 2 * Math.PI);
        ctx.fill();
    });
}

/**
 * تحديث الإحصائيات
 */
function updateStats() {
    const statCards = document.querySelectorAll('.stat-card');
    
    statCards.forEach(card => {
        const number = card.querySelector('.stat-number');
        if (number && number.dataset.countup) {
            animateNumber(number, parseInt(number.textContent) || 0);
        }
    });
}

/**
 * تحريك الأرقام
 */
function animateNumber(element, targetValue) {
    const duration = 2000; // 2 ثانية
    const startValue = 0;
    const startTime = Date.now();
    
    function updateNumber() {
        const currentTime = Date.now();
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // استخدام easing function
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const currentValue = Math.floor(startValue + (targetValue - startValue) * easeOutQuart);
        
        element.textContent = formatNumber(currentValue);
        
        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }
    
    updateNumber();
}

/**
 * تحديث الوقت الحقيقي
 */
function startRealTimeUpdates() {
    // تحديث الطلبات كل 30 ثانية
    setInterval(function() {
        updateOrdersCount();
    }, 30000);
    
    // تحديث الإحصائيات كل دقيقة
    setInterval(function() {
        updateDashboardStats();
    }, 60000);
}

/**
 * تحديث عدد الطلبات
 */
function updateOrdersCount() {
    fetch('api/orders-count.php')
        .then(response => response.json())
        .then(data => {
            const ordersElement = document.querySelector('[data-stat="orders"]');
            if (ordersElement && data.count !== undefined) {
                ordersElement.textContent = formatNumber(data.count);
            }
        })
        .catch(error => {
            console.error('خطأ في تحديث عدد الطلبات:', error);
        });
}

/**
 * تحديث إحصائيات لوحة التحكم
 */
function updateDashboardStats() {
    fetch('api/dashboard-stats.php')
        .then(response => response.json())
        .then(data => {
            // تحديث الإحصائيات
            Object.keys(data).forEach(key => {
                const element = document.querySelector(`[data-stat="${key}"]`);
                if (element) {
                    element.textContent = formatNumber(data[key]);
                }
            });
        })
        .catch(error => {
            console.error('خطأ في تحديث الإحصائيات:', error);
        });
}

/**
 * إظهار إشعار
 */
function showNotification(message, type = 'info', duration = 5000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    // إضافة الأنماط
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        left: 20px;
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        padding: 1rem;
        z-index: 10000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
        max-width: 400px;
    `;
    
    // ألوان حسب النوع
    const colors = {
        success: '#4CAF50',
        error: '#f44336',
        warning: '#ff9800',
        info: '#2196f3'
    };
    
    notification.style.borderLeft = `4px solid ${colors[type] || colors.info}`;
    
    document.body.appendChild(notification);
    
    // إظهار الإشعار
    setTimeout(() => {
        notification.style.transform = 'translateX(0)';
    }, 100);
    
    // إخفاء الإشعار تلقائياً
    const hideNotification = () => {
        notification.style.transform = 'translateX(-100%)';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    };
    
    // زر الإغلاق
    notification.querySelector('.notification-close').addEventListener('click', hideNotification);
    
    // إخفاء تلقائي
    if (duration > 0) {
        setTimeout(hideNotification, duration);
    }
}

// بدء التحديثات الحقيقية
startRealTimeUpdates();
