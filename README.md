# Menuz - منصة المنيوهات الرقمية للمطاعم

منصة ويب احترافية تتيح لأصحاب المطاعم إنشاء منيوهات رقمية تفاعلية مع نظام الطلبات والدفع الإلكتروني.

## 🌟 المميزات الرئيسية

### للمطاعم
- **منيو رقمية احترافية**: تصميم عصري ومتجاوب مع جميع الأجهزة
- **نظام طلبات متقدم**: استقبال الطلبات داخل وخارج المطعم
- **QR Code مخصص**: كود QR قابل للطباعة والتخصيص
- **دعم متعدد اللغات**: العربية والإنجليزية
- **إحصائيات مفصلة**: تتبع الأداء والمبيعات
- **دفع إلكتروني**: دعم PayPal وبطاقات الائتمان

### الباقات المتاحة

#### 🥉 باقة Basic ($3/شهر)
- منيو واحدة
- 15 منتج
- QR Code عادي
- طلبات داخلية فقط
- دعم فني أساسي

#### 🥈 باقة Pro ($6/شهر)
- منيو واحدة
- 50 منتج
- QR Code عادي
- طلبات داخلية وخارجية
- إحصائيات أساسية
- دعم فني متوسط

#### 🥇 باقة Ultra ($8/شهر)
- منيو واحدة
- منتجات غير محدودة
- QR Code مخصص
- تصميم مخصص
- طلبات داخلية وخارجية
- دفع إلكتروني
- إحصائيات متقدمة
- دعم VIP

## 🛠️ التقنيات المستخدمة

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Styling**: CSS Grid, Flexbox, Custom Properties
- **Fonts**: Cairo, Tajawal (خطوط عربية)
- **Icons**: Unicode Emojis
- **Security**: Password Hashing, SQL Injection Prevention, XSS Protection

## 📋 متطلبات النظام

- PHP 7.4 أو أحدث
- MySQL 5.7 أو أحدث
- Apache/Nginx Web Server
- mod_rewrite enabled
- GD Extension (لمعالجة الصور)
- cURL Extension (للدفع الإلكتروني)

## 🚀 التثبيت والإعداد

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/menuz.git
cd menuz
```

### 2. إعداد قاعدة البيانات
1. إنشاء قاعدة بيانات جديدة:
```sql
CREATE DATABASE menuz_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. استيراد الجداول:
```bash
mysql -u username -p menuz_db < database/schema.sql
```

### 3. تكوين الإعدادات
قم بتعديل ملف `config/database.php`:
```php
define('DB_HOST', 'localhost');
define('DB_NAME', 'menuz_db');
define('DB_USER', 'your_username');
define('DB_PASS', 'your_password');

define('SITE_URL', 'http://localhost/menuz');
```

### 4. إعداد الصلاحيات
```bash
chmod 755 uploads/
chmod 644 config/database.php
```

### 5. تشغيل الموقع
- ضع الملفات في مجلد الويب (htdocs/www)
- تأكد من تشغيل Apache/Nginx و MySQL
- افتح المتصفح وانتقل إلى: `http://localhost/menuz`

## 📁 هيكل المشروع

```
menuz/
├── assets/                 # الملفات الثابتة
│   ├── css/               # ملفات التنسيق
│   ├── js/                # ملفات JavaScript
│   └── images/            # الصور
├── auth/                  # نظام المصادقة
│   ├── login.php          # تسجيل الدخول
│   ├── register.php       # التسجيل
│   └── logout.php         # تسجيل الخروج
├── config/                # ملفات التكوين
│   └── database.php       # إعدادات قاعدة البيانات
├── dashboard/             # لوحة التحكم
│   ├── includes/          # ملفات مشتركة
│   ├── products/          # إدارة المنتجات
│   ├── orders/            # إدارة الطلبات
│   ├── analytics/         # الإحصائيات
│   └── index.php          # الصفحة الرئيسية
├── database/              # قاعدة البيانات
│   └── schema.sql         # هيكل الجداول
├── includes/              # دوال مساعدة
│   └── functions.php      # الدوال العامة
├── public/                # الصفحات العامة
│   ├── home.php           # الصفحة الرئيسية
│   └── menu.php           # عرض المنيو
├── uploads/               # الملفات المرفوعة
└── index.php              # نقطة الدخول الرئيسية
```

## 🔧 الاستخدام

### للمطاعم
1. **التسجيل**: إنشاء حساب جديد
2. **إعداد المطعم**: ملء معلومات المطعم واختيار الباقة
3. **إضافة المنتجات**: إنشاء قائمة الطعام
4. **مشاركة المنيو**: استخدام QR Code أو الرابط المباشر
5. **إدارة الطلبات**: متابعة وإدارة الطلبات الواردة

### للعملاء
1. **مسح QR Code**: أو فتح رابط المنيو
2. **تصفح المنيو**: عرض المنتجات والأسعار
3. **تقديم الطلب**: اختيار المنتجات وملء البيانات
4. **الدفع**: (في باقة Ultra) الدفع الإلكتروني المباشر

## 🔒 الأمان

- **تشفير كلمات المرور**: استخدام password_hash()
- **حماية من SQL Injection**: استخدام Prepared Statements
- **حماية من XSS**: تنظيف المدخلات
- **إدارة الجلسات**: جلسات آمنة ومحدودة الوقت
- **التحقق من الصلاحيات**: فحص صلاحيات المستخدم

## 🎨 التخصيص

### الألوان
يمكن تخصيص الألوان من خلال CSS Variables في `assets/css/style.css`:
```css
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
}
```

### الخطوط
الموقع يستخدم خطوط عربية احترافية:
- Cairo: للنصوص العامة
- Tajawal: للعناوين

## 📱 الاستجابة

الموقع مصمم ليكون متجاوب بالكامل مع:
- أجهزة الكمبيوتر المكتبية
- الأجهزة اللوحية
- الهواتف الذكية

## 🌐 دعم المتصفحات

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 📞 الدعم الفني

- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +966 50 123 4567
- **الموقع**: www.menuz.com

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT. راجع ملف `LICENSE` للمزيد من التفاصيل.

## 🤝 المساهمة

نرحب بالمساهمات! يرجى قراءة `CONTRIBUTING.md` للمزيد من المعلومات.

## 📈 خطط المستقبل

- [ ] تطبيق جوال (iOS/Android)
- [ ] دعم المزيد من بوابات الدفع
- [ ] نظام الولاء والنقاط
- [ ] تقييمات العملاء
- [ ] دعم المزيد من اللغات
- [ ] API للتكامل مع أنظمة أخرى

---

**تم تطوير هذا المشروع بـ ❤️ لخدمة المطاعم العربية**
