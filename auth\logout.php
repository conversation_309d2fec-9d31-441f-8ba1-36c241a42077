<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// تسجيل النشاط قبل تسجيل الخروج
if (is_logged_in()) {
    try {
        $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, action, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?)");
        $stmt->execute([
            $_SESSION['user_id'],
            'logout',
            'تسجيل خروج',
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (PDOException $e) {
        // تجاهل الخطأ
    }
}

// حذف كوكيز التذكر
if (isset($_COOKIE['remember_token'])) {
    setcookie('remember_token', '', time() - 3600, '/');
    
    // حذف التوكن من قاعدة البيانات
    if (is_logged_in()) {
        try {
            $stmt = $pdo->prepare("UPDATE users SET remember_token = NULL WHERE id = ?");
            $stmt->execute([$_SESSION['user_id']]);
        } catch (PDOException $e) {
            // تجاهل الخطأ
        }
    }
}

// تدمير الجلسة
session_destroy();

// إعادة التوجيه
redirect('../auth/login.php?message=تم تسجيل الخروج بنجاح');
?>
