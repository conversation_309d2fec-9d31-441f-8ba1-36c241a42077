# حماية مجلد الرفع - منع تنفيذ ملفات PHP

# منع تنفيذ جميع ملفات PHP
<FilesMatch "\.php$">
    Require all denied
</FilesMatch>

# منع تنفيذ ملفات البرمجة الأخرى
<FilesMatch "\.(php|php3|php4|php5|phtml|pl|py|jsp|asp|sh|cgi)$">
    Require all denied
</FilesMatch>

# السماح بالصور فقط
<FilesMatch "\.(jpg|jpeg|png|gif|svg|webp|bmp|ico)$">
    Require all granted
</FilesMatch>

# منع الوصول للملفات المخفية
<FilesMatch "^\.">
    Require all denied
</FilesMatch>

# منع عرض قائمة الملفات
Options -Indexes

# إعدادات MIME types للصور
<IfModule mod_mime.c>
    AddType image/jpeg .jpg .jpeg
    AddType image/png .png
    AddType image/gif .gif
    AddType image/svg+xml .svg
    AddType image/webp .webp
    AddType image/bmp .bmp
    AddType image/x-icon .ico
</IfModule>

# منع hotlinking للصور
<IfModule mod_rewrite.c>
    RewriteEngine On
    RewriteCond %{HTTP_REFERER} !^$
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?bag\.meedps\.com [NC]
    RewriteCond %{HTTP_REFERER} !^https?://(www\.)?meedps\.com [NC]
    RewriteRule \.(jpg|jpeg|png|gif|webp|svg)$ - [NC,F,L]
</IfModule>

# تحسين الأداء للصور
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 30 days"
    ExpiresByType image/jpeg "access plus 30 days"
    ExpiresByType image/png "access plus 30 days"
    ExpiresByType image/gif "access plus 30 days"
    ExpiresByType image/webp "access plus 30 days"
    ExpiresByType image/svg+xml "access plus 30 days"
    ExpiresByType image/x-icon "access plus 1 year"
</IfModule>

# ضغط الصور
<IfModule mod_headers.c>
    <FilesMatch "\.(jpg|jpeg|png|gif|webp|svg)$">
        Header set Cache-Control "public, max-age=2592000"
        Header unset ETag
        FileETag None
    </FilesMatch>
</IfModule>
