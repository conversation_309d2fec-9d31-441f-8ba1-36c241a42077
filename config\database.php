<?php
// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'menuz_db');
define('DB_USER', 'root');
define('DB_PASS', '');

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
}

// إعدادات عامة للموقع
define('SITE_URL', 'http://localhost/Menu');
define('SITE_NAME', 'Menuz');
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// إعدادات الباقات
define('BASIC_PRICE', 3);
define('PRO_PRICE', 6);
define('ULTRA_PRICE', 8);

define('BASIC_PRODUCTS_LIMIT', 15);
define('PRO_PRODUCTS_LIMIT', 50);
define('ULTRA_PRODUCTS_LIMIT', -1); // غير محدود
?>
