<?php
// تحميل إعدادات الاستضافة المشتركة
require_once dirname(__FILE__) . '/hosting.php';

// إعدادات قاعدة البيانات للاستضافة المشتركة
define('DB_HOST', 'localhost');
define('DB_NAME', 'meedpsco_Menu');
define('DB_USER', 'meedpsco_Menu');
define('DB_PASS', 'meedpsco_Menu');

try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4", DB_USER, DB_PASS, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_PERSISTENT => false, // تجنب الاتصالات المستمرة في الاستضافة المشتركة
        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
        PDO::ATTR_TIMEOUT => 30, // مهلة زمنية للاتصال
    ]);

    // التحقق من الاتصال
    if (!check_database_connection()) {
        throw new PDOException('فشل في التحقق من اتصال قاعدة البيانات');
    }

} catch(PDOException $e) {
    log_error('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());

    if (DEBUG_MODE) {
        die("خطأ في الاتصال بقاعدة البيانات: " . $e->getMessage());
    } else {
        header("HTTP/1.0 503 Service Unavailable");
        die("الموقع غير متاح مؤقتاً. يرجى المحاولة لاحقاً.");
    }
}

// إعدادات عامة للموقع
define('SITE_URL', 'https://bag.meedps.com');
define('SITE_NAME', 'Menuz');
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB

// إعدادات الباقات
define('BASIC_PRICE', 3);
define('PRO_PRICE', 6);
define('ULTRA_PRICE', 8);

define('BASIC_PRODUCTS_LIMIT', 15);
define('PRO_PRODUCTS_LIMIT', 50);
define('ULTRA_PRODUCTS_LIMIT', -1); // غير محدود
?>
