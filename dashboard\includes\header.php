<?php
// الحصول على معلومات المستخدم إذا لم تكن محملة
if (!isset($user_data)) {
    try {
        $stmt = $pdo->prepare("
            SELECT u.*, r.name as restaurant_name, r.slug,
                   s.plan_id, s.status as subscription_status, s.end_date,
                   p.name_ar as plan_name_ar
            FROM users u
            LEFT JOIN restaurants r ON u.id = r.user_id
            LEFT JOIN subscriptions s ON u.id = s.user_id AND s.status = 'active'
            LEFT JOIN plans p ON s.plan_id = p.id
            WHERE u.id = ?
        ");
        $stmt->execute([$_SESSION['user_id']]);
        $user_data = $stmt->fetch();
    } catch (PDOException $e) {
        // تجاهل الخطأ
    }
}

// الحصول على عدد الطلبات الجديدة
$new_orders_count = 0;
if (isset($user_data['restaurant_id']) && $user_data['restaurant_id']) {
    try {
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE restaurant_id = ? AND status = 'pending'");
        $stmt->execute([$user_data['restaurant_id']]);
        $new_orders_count = $stmt->fetchColumn();
    } catch (PDOException $e) {
        // تجاهل الخطأ
    }
}

// الحصول على الإشعارات
$notifications = [];
if (isset($user_data['restaurant_id']) && $user_data['restaurant_id']) {
    try {
        // طلبات جديدة
        if ($new_orders_count > 0) {
            $notifications[] = [
                'type' => 'order',
                'title' => 'طلبات جديدة',
                'message' => "لديك {$new_orders_count} طلب جديد",
                'time' => 'الآن',
                'url' => '../dashboard/orders/'
            ];
        }
        
        // تنبيه انتهاء الاشتراك
        if ($user_data['end_date'] && strtotime($user_data['end_date']) - time() < 7 * 24 * 60 * 60) {
            $days_left = ceil((strtotime($user_data['end_date']) - time()) / (24 * 60 * 60));
            $notifications[] = [
                'type' => 'subscription',
                'title' => 'انتهاء الاشتراك',
                'message' => "اشتراكك سينتهي خلال {$days_left} أيام",
                'time' => 'تنبيه',
                'url' => '../dashboard/subscription.php'
            ];
        }
    } catch (PDOException $e) {
        // تجاهل الخطأ
    }
}
?>

<header class="dashboard-header">
    <div class="header-left">
        <button class="menu-toggle">
            <span>☰</span>
        </button>
        
        <div class="breadcrumb">
            <?php
            $page_title = 'لوحة التحكم';
            $current_page = basename($_SERVER['PHP_SELF']);
            $current_dir = basename(dirname($_SERVER['PHP_SELF']));
            
            // تحديد عنوان الصفحة
            switch ($current_dir) {
                case 'products':
                    $page_title = 'المنتجات';
                    break;
                case 'orders':
                    $page_title = 'الطلبات';
                    break;
                case 'analytics':
                    $page_title = 'الإحصائيات';
                    break;
                case 'restaurant':
                    $page_title = 'إعدادات المطعم';
                    break;
                default:
                    switch ($current_page) {
                        case 'subscription.php':
                            $page_title = 'الاشتراك';
                            break;
                        case 'profile.php':
                            $page_title = 'الملف الشخصي';
                            break;
                        case 'settings.php':
                            $page_title = 'الإعدادات';
                            break;
                        case 'qr-code.php':
                            $page_title = 'QR Code';
                            break;
                        case 'menu-preview.php':
                            $page_title = 'معاينة المنيو';
                            break;
                    }
            }
            ?>
            <h1 class="page-title"><?php echo $page_title; ?></h1>
        </div>
    </div>
    
    <div class="header-right">
        <!-- زر معاينة المنيو -->
        <?php if (isset($user_data['slug']) && $user_data['slug']): ?>
            <a href="<?php echo SITE_URL; ?>/?restaurant=<?php echo $user_data['slug']; ?>" 
               target="_blank" 
               class="btn btn-secondary btn-sm"
               title="معاينة المنيو">
                <span>👁️</span>
                <span class="btn-text">معاينة المنيو</span>
            </a>
        <?php endif; ?>
        
        <!-- الإشعارات -->
        <div class="notifications-menu">
            <button class="notifications-toggle" title="الإشعارات">
                <span class="notification-icon">🔔</span>
                <?php if (count($notifications) > 0): ?>
                    <span class="notification-badge"><?php echo count($notifications); ?></span>
                <?php endif; ?>
            </button>
            
            <div class="notifications-dropdown">
                <div class="notifications-header">
                    <h3>الإشعارات</h3>
                    <?php if (count($notifications) > 0): ?>
                        <button class="mark-all-read">تعيين الكل كمقروء</button>
                    <?php endif; ?>
                </div>
                
                <div class="notifications-list">
                    <?php if (empty($notifications)): ?>
                        <div class="no-notifications">
                            <span>لا توجد إشعارات جديدة</span>
                        </div>
                    <?php else: ?>
                        <?php foreach ($notifications as $notification): ?>
                            <a href="<?php echo $notification['url']; ?>" class="notification-item">
                                <div class="notification-content">
                                    <div class="notification-title"><?php echo $notification['title']; ?></div>
                                    <div class="notification-message"><?php echo $notification['message']; ?></div>
                                    <div class="notification-time"><?php echo $notification['time']; ?></div>
                                </div>
                                <div class="notification-type notification-<?php echo $notification['type']; ?>"></div>
                            </a>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                
                <?php if (count($notifications) > 3): ?>
                    <div class="notifications-footer">
                        <a href="../dashboard/notifications.php">عرض جميع الإشعارات</a>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- قائمة المستخدم -->
        <div class="user-menu">
            <div class="user-avatar" title="<?php echo htmlspecialchars($_SESSION['user_name']); ?>">
                <?php echo strtoupper(substr($user_data['first_name'], 0, 1)); ?>
            </div>
            
            <div class="user-dropdown">
                <div class="user-info">
                    <div class="user-name"><?php echo htmlspecialchars($_SESSION['user_name']); ?></div>
                    <div class="user-email"><?php echo htmlspecialchars($_SESSION['user_email']); ?></div>
                    <?php if (isset($user_data['plan_name_ar'])): ?>
                        <div class="user-plan">باقة <?php echo $user_data['plan_name_ar']; ?></div>
                    <?php endif; ?>
                </div>
                
                <div class="dropdown-divider"></div>
                
                <a href="../dashboard/profile.php" class="dropdown-item">
                    <span>👤</span>
                    <span>الملف الشخصي</span>
                </a>
                
                <a href="../dashboard/settings.php" class="dropdown-item">
                    <span>⚙️</span>
                    <span>الإعدادات</span>
                </a>
                
                <a href="../dashboard/help.php" class="dropdown-item">
                    <span>❓</span>
                    <span>المساعدة</span>
                </a>
                
                <div class="dropdown-divider"></div>
                
                <a href="../auth/logout.php" class="dropdown-item text-danger">
                    <span>🚪</span>
                    <span>تسجيل الخروج</span>
                </a>
            </div>
        </div>
    </div>
</header>

<style>
/* تنسيقات هيدر لوحة التحكم */
.dashboard-header {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-bottom: 1px solid var(--border-color);
    backdrop-filter: blur(10px);
}

.page-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.btn-text {
    margin-right: 0.5rem;
}

/* الإشعارات */
.notifications-menu {
    position: relative;
}

.notifications-toggle {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    position: relative;
    padding: 0.5rem;
    border-radius: 50%;
    transition: background 0.3s ease;
}

.notifications-toggle:hover {
    background: var(--bg-light);
}

.notification-badge {
    position: absolute;
    top: 0;
    left: 0;
    background: var(--error-color);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
    line-height: 1;
}

.notifications-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    min-width: 350px;
    max-width: 400px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
    border: 1px solid var(--border-color);
}

.notifications-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.notifications-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.notifications-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
}

.mark-all-read {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.9rem;
    cursor: pointer;
    text-decoration: underline;
}

.notifications-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    padding: 1rem 1.5rem;
    text-decoration: none;
    color: var(--text-dark);
    border-bottom: 1px solid var(--border-color);
    transition: background 0.3s ease;
    position: relative;
}

.notification-item:hover {
    background: var(--bg-light);
}

.notification-item:last-child {
    border-bottom: none;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
    color: var(--text-dark);
}

.notification-message {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

.notification-time {
    font-size: 0.8rem;
    color: var(--text-light);
}

.notification-type {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-top: 0.5rem;
    margin-right: 1rem;
}

.notification-order {
    background: var(--primary-color);
}

.notification-subscription {
    background: var(--warning-color);
}

.no-notifications {
    padding: 2rem;
    text-align: center;
    color: var(--text-light);
}

.notifications-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.notifications-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: 500;
}

/* معلومات المستخدم */
.user-info {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid var(--border-color);
}

.user-name {
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.25rem;
}

.user-email {
    font-size: 0.9rem;
    color: var(--text-light);
    margin-bottom: 0.25rem;
}

.user-plan {
    font-size: 0.8rem;
    color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    display: inline-block;
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    color: var(--text-dark);
    text-decoration: none;
    transition: background 0.3s ease;
}

.dropdown-item:hover {
    background: var(--bg-light);
}

.dropdown-item.text-danger {
    color: var(--error-color);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1rem;
    }
    
    .header-right {
        gap: 0.5rem;
    }
    
    .btn-text {
        display: none;
    }
    
    .notifications-dropdown {
        min-width: 300px;
        right: 0;
        left: auto;
    }
    
    .user-dropdown {
        right: 0;
        left: auto;
    }
    
    .page-title {
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .notifications-dropdown {
        min-width: 280px;
        max-width: 90vw;
    }
    
    .notification-item {
        padding: 0.75rem 1rem;
    }
    
    .notifications-header {
        padding: 0.75rem 1rem;
    }
}
</style>

<script>
// تهيئة الإشعارات
document.addEventListener('DOMContentLoaded', function() {
    const notificationsToggle = document.querySelector('.notifications-toggle');
    const notificationsDropdown = document.querySelector('.notifications-dropdown');
    
    if (notificationsToggle && notificationsDropdown) {
        notificationsToggle.addEventListener('click', function(e) {
            e.stopPropagation();
            notificationsDropdown.classList.toggle('show');
        });
        
        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', function(e) {
            if (!notificationsToggle.contains(e.target) && !notificationsDropdown.contains(e.target)) {
                notificationsDropdown.classList.remove('show');
            }
        });
    }
    
    // تعيين جميع الإشعارات كمقروءة
    const markAllRead = document.querySelector('.mark-all-read');
    if (markAllRead) {
        markAllRead.addEventListener('click', function() {
            // إرسال طلب AJAX لتعيين الإشعارات كمقروءة
            fetch('../api/mark-notifications-read.php', {
                method: 'POST'
            }).then(() => {
                location.reload();
            });
        });
    }
});
</script>
