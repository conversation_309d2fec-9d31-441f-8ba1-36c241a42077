<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    redirect('../auth/login.php');
}

$user_id = $_SESSION['user_id'];

// الحصول على معلومات المستخدم والمطعم
try {
    $stmt = $pdo->prepare("
        SELECT u.*, r.id as restaurant_id, r.name as restaurant_name, r.slug, r.status as restaurant_status,
               s.plan_id, s.status as subscription_status, s.end_date,
               p.name as plan_name, p.name_ar as plan_name_ar, p.features
        FROM users u
        LEFT JOIN restaurants r ON u.id = r.user_id
        LEFT JOIN subscriptions s ON u.id = s.user_id AND s.status = 'active'
        LEFT JOIN plans p ON s.plan_id = p.id
        WHERE u.id = ?
    ");
    $stmt->execute([$user_id]);
    $user_data = $stmt->fetch();
    
    if (!$user_data) {
        redirect('../auth/logout.php');
    }
    
    // إحصائيات سريعة
    $stats = [];
    
    if ($user_data['restaurant_id']) {
        // عدد المنتجات
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM products WHERE restaurant_id = ? AND status = 'active'");
        $stmt->execute([$user_data['restaurant_id']]);
        $stats['products'] = $stmt->fetchColumn();
        
        // عدد الطلبات اليوم
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM orders WHERE restaurant_id = ? AND DATE(created_at) = CURDATE()");
        $stmt->execute([$user_data['restaurant_id']]);
        $stats['orders_today'] = $stmt->fetchColumn();
        
        // إجمالي المبيعات هذا الشهر
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE restaurant_id = ? AND MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE()) AND status != 'cancelled'");
        $stmt->execute([$user_data['restaurant_id']]);
        $stats['revenue_month'] = $stmt->fetchColumn();
        
        // الزيارات هذا الشهر
        $stmt = $pdo->prepare("SELECT COALESCE(SUM(page_views), 0) FROM analytics WHERE restaurant_id = ? AND MONTH(date) = MONTH(CURDATE()) AND YEAR(date) = YEAR(CURDATE())");
        $stmt->execute([$user_data['restaurant_id']]);
        $stats['views_month'] = $stmt->fetchColumn();
        
        // الطلبات الحديثة
        $stmt = $pdo->prepare("SELECT * FROM orders WHERE restaurant_id = ? ORDER BY created_at DESC LIMIT 5");
        $stmt->execute([$user_data['restaurant_id']]);
        $recent_orders = $stmt->fetchAll();
    }
    
} catch (PDOException $e) {
    $error = 'حدث خطأ في النظام';
}

// التحقق من حالة الاشتراك
$subscription_warning = '';
if ($user_data['subscription_status'] == 'expired' || !$user_data['plan_id']) {
    $subscription_warning = 'اشتراكك منتهي الصلاحية. يرجى تجديد الاشتراك للاستمرار في استخدام الخدمة.';
} elseif ($user_data['end_date'] && strtotime($user_data['end_date']) - time() < 7 * 24 * 60 * 60) {
    $subscription_warning = 'اشتراكك سينتهي قريباً في ' . format_date_arabic($user_data['end_date']) . '. يرجى التجديد.';
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة التحكم - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="../assets/css/dashboard.css">
</head>
<body>
    <!-- الشريط الجانبي -->
    <?php include 'includes/sidebar.php'; ?>
    
    <!-- المحتوى الرئيسي -->
    <div class="main-content">
        <!-- الهيدر -->
        <?php include 'includes/header.php'; ?>
        
        <!-- المحتوى -->
        <div class="content">
            <div class="container">
                <!-- ترحيب -->
                <div class="welcome-section">
                    <h1>مرحباً، <?php echo htmlspecialchars($user_data['first_name']); ?>!</h1>
                    <p>إليك نظرة سريعة على أداء مطعمك اليوم</p>
                </div>
                
                <!-- تنبيه الاشتراك -->
                <?php if ($subscription_warning): ?>
                    <div class="alert alert-warning">
                        <strong>تنبيه:</strong> <?php echo $subscription_warning; ?>
                        <a href="subscription.php" class="btn btn-primary btn-sm" style="margin-right: 1rem;">تجديد الاشتراك</a>
                    </div>
                <?php endif; ?>
                
                <?php if (!$user_data['restaurant_id']): ?>
                    <!-- إعداد المطعم -->
                    <div class="setup-card">
                        <div class="card">
                            <div class="card-body text-center">
                                <h2>أنشئ مطعمك الأول</h2>
                                <p>لم تقم بإنشاء مطعم بعد. ابدأ الآن في إنشاء منيو رقمية احترافية لمطعمك.</p>
                                <a href="setup.php" class="btn btn-primary">إنشاء مطعم جديد</a>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <!-- الإحصائيات السريعة -->
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">🍽️</div>
                            <div class="stat-info">
                                <div class="stat-number"><?php echo number_format($stats['products']); ?></div>
                                <div class="stat-label">المنتجات</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">📋</div>
                            <div class="stat-info">
                                <div class="stat-number"><?php echo number_format($stats['orders_today']); ?></div>
                                <div class="stat-label">طلبات اليوم</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">💰</div>
                            <div class="stat-info">
                                <div class="stat-number">$<?php echo number_format($stats['revenue_month'], 2); ?></div>
                                <div class="stat-label">مبيعات الشهر</div>
                            </div>
                        </div>
                        
                        <div class="stat-card">
                            <div class="stat-icon">👁️</div>
                            <div class="stat-info">
                                <div class="stat-number"><?php echo number_format($stats['views_month']); ?></div>
                                <div class="stat-label">زيارات الشهر</div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- الإجراءات السريعة -->
                    <div class="quick-actions">
                        <h2>إجراءات سريعة</h2>
                        <div class="actions-grid">
                            <a href="products/add.php" class="action-card">
                                <div class="action-icon">➕</div>
                                <div class="action-title">إضافة منتج</div>
                                <div class="action-desc">أضف منتج جديد إلى منيوك</div>
                            </a>
                            
                            <a href="orders/" class="action-card">
                                <div class="action-icon">📋</div>
                                <div class="action-title">إدارة الطلبات</div>
                                <div class="action-desc">عرض وإدارة الطلبات الواردة</div>
                            </a>
                            
                            <a href="menu-preview.php" class="action-card">
                                <div class="action-icon">👁️</div>
                                <div class="action-title">معاينة المنيو</div>
                                <div class="action-desc">شاهد كيف يبدو منيوك للعملاء</div>
                            </a>
                            
                            <a href="qr-code.php" class="action-card">
                                <div class="action-icon">📱</div>
                                <div class="action-title">QR Code</div>
                                <div class="action-desc">تحميل كود QR لمطعمك</div>
                            </a>
                        </div>
                    </div>
                    
                    <!-- الطلبات الحديثة -->
                    <?php if (!empty($recent_orders)): ?>
                        <div class="recent-orders">
                            <h2>الطلبات الحديثة</h2>
                            <div class="card">
                                <div class="table-responsive">
                                    <table class="table">
                                        <thead>
                                            <tr>
                                                <th>رقم الطلب</th>
                                                <th>العميل</th>
                                                <th>المبلغ</th>
                                                <th>الحالة</th>
                                                <th>التاريخ</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($recent_orders as $order): ?>
                                                <tr>
                                                    <td>#<?php echo htmlspecialchars($order['order_number']); ?></td>
                                                    <td><?php echo htmlspecialchars($order['customer_name']); ?></td>
                                                    <td>$<?php echo number_format($order['total_amount'], 2); ?></td>
                                                    <td>
                                                        <span class="status-badge status-<?php echo $order['status']; ?>">
                                                            <?php echo get_order_status_text($order['status']); ?>
                                                        </span>
                                                    </td>
                                                    <td><?php echo format_date_arabic($order['created_at']); ?></td>
                                                    <td>
                                                        <a href="orders/view.php?id=<?php echo $order['id']; ?>" class="btn btn-sm btn-primary">عرض</a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                                <div class="card-footer text-center">
                                    <a href="orders/" class="btn btn-secondary">عرض جميع الطلبات</a>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                    
                    <!-- معلومات المطعم -->
                    <div class="restaurant-info">
                        <h2>معلومات المطعم</h2>
                        <div class="card">
                            <div class="card-body">
                                <div class="info-grid">
                                    <div class="info-item">
                                        <strong>اسم المطعم:</strong>
                                        <span><?php echo htmlspecialchars($user_data['restaurant_name']); ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>رابط المنيو:</strong>
                                        <span>
                                            <a href="<?php echo SITE_URL; ?>/?restaurant=<?php echo $user_data['slug']; ?>" target="_blank">
                                                <?php echo SITE_URL; ?>/?restaurant=<?php echo $user_data['slug']; ?>
                                            </a>
                                        </span>
                                    </div>
                                    <div class="info-item">
                                        <strong>الباقة الحالية:</strong>
                                        <span><?php echo $user_data['plan_name_ar'] ?? 'غير محدد'; ?></span>
                                    </div>
                                    <div class="info-item">
                                        <strong>تاريخ انتهاء الاشتراك:</strong>
                                        <span><?php echo $user_data['end_date'] ? format_date_arabic($user_data['end_date']) : 'غير محدد'; ?></span>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <a href="restaurant/edit.php" class="btn btn-primary">تعديل معلومات المطعم</a>
                                    <a href="subscription.php" class="btn btn-secondary">إدارة الاشتراك</a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/main.js"></script>
    <script src="../assets/js/dashboard.js"></script>
</body>
</html>

<?php
function get_order_status_text($status) {
    $statuses = [
        'pending' => 'في الانتظار',
        'confirmed' => 'مؤكد',
        'preparing' => 'قيد التحضير',
        'ready' => 'جاهز',
        'delivered' => 'تم التسليم',
        'cancelled' => 'ملغي'
    ];
    
    return $statuses[$status] ?? $status;
}
?>
