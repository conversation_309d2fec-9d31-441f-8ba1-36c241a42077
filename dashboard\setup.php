<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// التحقق من تسجيل الدخول
if (!is_logged_in()) {
    redirect('../auth/login.php');
}

$user_id = $_SESSION['user_id'];
$error = '';
$success = '';

// التحقق من وجود مطعم للمستخدم
try {
    $stmt = $pdo->prepare("SELECT id FROM restaurants WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $existing_restaurant = $stmt->fetch();
    
    if ($existing_restaurant) {
        redirect('index.php');
    }
} catch (PDOException $e) {
    $error = 'حدث خطأ في النظام';
}

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $restaurant_name = sanitize_input($_POST['restaurant_name']);
    $restaurant_name_en = sanitize_input($_POST['restaurant_name_en']);
    $description = sanitize_input($_POST['description']);
    $description_en = sanitize_input($_POST['description_en']);
    $phone = sanitize_input($_POST['phone']);
    $address = sanitize_input($_POST['address']);
    $address_en = sanitize_input($_POST['address_en']);
    $plan_id = (int)$_POST['plan_id'];
    
    // التحقق من البيانات
    if (empty($restaurant_name) || empty($plan_id)) {
        $error = 'اسم المطعم والباقة مطلوبان';
    } else {
        // إنشاء slug فريد
        $slug = create_unique_slug($restaurant_name, $pdo);
        
        try {
            $pdo->beginTransaction();
            
            // إنشاء المطعم
            $stmt = $pdo->prepare("
                INSERT INTO restaurants (user_id, name, name_en, slug, description, description_en, phone, address, address_en) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $user_id, $restaurant_name, $restaurant_name_en, $slug, 
                $description, $description_en, $phone, $address, $address_en
            ]);
            
            $restaurant_id = $pdo->lastInsertId();
            
            // إنشاء الاشتراك
            $plan_info = get_plan_info_by_id($plan_id, $pdo);
            if ($plan_info) {
                $start_date = date('Y-m-d');
                $end_date = date('Y-m-d', strtotime('+1 month'));
                
                $stmt = $pdo->prepare("
                    INSERT INTO subscriptions (user_id, plan_id, status, start_date, end_date, amount) 
                    VALUES (?, ?, 'active', ?, ?, ?)
                ");
                $stmt->execute([$user_id, $plan_id, $start_date, $end_date, $plan_info['price']]);
            }
            
            // إنشاء فئة افتراضية
            $stmt = $pdo->prepare("
                INSERT INTO categories (restaurant_id, name, name_en, description, description_en) 
                VALUES (?, 'الأطباق الرئيسية', 'Main Dishes', 'أطباق رئيسية متنوعة', 'Various main dishes')
            ");
            $stmt->execute([$restaurant_id]);
            
            // تسجيل النشاط
            $stmt = $pdo->prepare("
                INSERT INTO activity_logs (user_id, restaurant_id, action, description, ip_address, user_agent) 
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $user_id, $restaurant_id, 'restaurant_created', 
                'تم إنشاء مطعم جديد: ' . $restaurant_name,
                $_SERVER['REMOTE_ADDR'] ?? '',
                $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]);
            
            $pdo->commit();
            
            $success = 'تم إنشاء مطعمك بنجاح! يمكنك الآن البدء في إضافة المنتجات.';
            
            // إعادة التوجيه بعد 2 ثانية
            header("refresh:2;url=index.php");
            
        } catch (PDOException $e) {
            $pdo->rollBack();
            $error = 'حدث خطأ في إنشاء المطعم';
        }
    }
}

// الحصول على الباقات المتاحة
try {
    $stmt = $pdo->prepare("SELECT * FROM plans WHERE status = 'active' ORDER BY price ASC");
    $stmt->execute();
    $plans = $stmt->fetchAll();
} catch (PDOException $e) {
    $plans = [];
}

function create_unique_slug($name, $pdo) {
    $slug = strtolower(trim($name));
    $slug = preg_replace('/[^a-z0-9\-]/', '-', $slug);
    $slug = preg_replace('/-+/', '-', $slug);
    $slug = trim($slug, '-');
    
    // التحقق من الفرادة
    $original_slug = $slug;
    $counter = 1;
    
    while (true) {
        $stmt = $pdo->prepare("SELECT id FROM restaurants WHERE slug = ?");
        $stmt->execute([$slug]);
        
        if (!$stmt->fetch()) {
            break;
        }
        
        $slug = $original_slug . '-' . $counter;
        $counter++;
    }
    
    return $slug;
}

function get_plan_info_by_id($plan_id, $pdo) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM plans WHERE id = ?");
        $stmt->execute([$plan_id]);
        return $stmt->fetch();
    } catch (PDOException $e) {
        return null;
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد المطعم - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .setup-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .setup-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 800px;
        }
        
        .setup-header {
            text-align: center;
            margin-bottom: 3rem;
        }
        
        .setup-logo {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 1rem;
        }
        
        .setup-title {
            font-size: 2rem;
            font-weight: 600;
            color: var(--text-dark);
            margin-bottom: 0.5rem;
        }
        
        .setup-subtitle {
            color: var(--text-light);
            font-size: 1.1rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }
        
        .plans-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin-bottom: 2rem;
        }
        
        .plan-card {
            border: 2px solid var(--border-color);
            border-radius: 12px;
            padding: 1.5rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .plan-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }
        
        .plan-card.selected {
            border-color: var(--primary-color);
            background: rgba(102, 126, 234, 0.05);
        }
        
        .plan-card.featured {
            border-color: var(--primary-color);
            position: relative;
        }
        
        .plan-badge {
            position: absolute;
            top: -10px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--primary-color);
            color: white;
            padding: 0.3rem 1rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .plan-name {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
            color: var(--text-dark);
        }
        
        .plan-price {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .plan-period {
            color: var(--text-light);
            margin-bottom: 1rem;
        }
        
        .plan-features {
            list-style: none;
            text-align: right;
        }
        
        .plan-features li {
            padding: 0.25rem 0;
            color: var(--text-dark);
            font-size: 0.9rem;
            position: relative;
            padding-right: 1.5rem;
        }
        
        .plan-features li::before {
            content: '✓';
            position: absolute;
            right: 0;
            color: var(--success-color);
            font-weight: bold;
        }
        
        .plan-radio {
            display: none;
        }
        
        .setup-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            margin-top: 2rem;
        }
        
        @media (max-width: 768px) {
            .setup-card {
                padding: 2rem;
                margin: 1rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
            }
            
            .plans-grid {
                grid-template-columns: 1fr;
            }
            
            .setup-actions {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="setup-container">
        <div class="setup-card">
            <div class="setup-header">
                <div class="setup-logo"><?php echo SITE_NAME; ?></div>
                <h1 class="setup-title">أنشئ مطعمك الأول</h1>
                <p class="setup-subtitle">املأ المعلومات التالية لإنشاء منيو رقمية احترافية لمطعمك</p>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <!-- معلومات المطعم الأساسية -->
                <div class="form-row">
                    <div class="form-group">
                        <label for="restaurant_name" class="form-label">اسم المطعم (بالعربية) *</label>
                        <input type="text" id="restaurant_name" name="restaurant_name" class="form-control" 
                               placeholder="مطعم الذواقة" required 
                               value="<?php echo isset($_POST['restaurant_name']) ? htmlspecialchars($_POST['restaurant_name']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="restaurant_name_en" class="form-label">اسم المطعم (بالإنجليزية)</label>
                        <input type="text" id="restaurant_name_en" name="restaurant_name_en" class="form-control" 
                               placeholder="Gourmet Restaurant" 
                               value="<?php echo isset($_POST['restaurant_name_en']) ? htmlspecialchars($_POST['restaurant_name_en']) : ''; ?>">
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="description" class="form-label">وصف المطعم (بالعربية)</label>
                        <textarea id="description" name="description" class="form-control" rows="3" 
                                  placeholder="وصف مختصر عن مطعمك وما يميزه"><?php echo isset($_POST['description']) ? htmlspecialchars($_POST['description']) : ''; ?></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label for="description_en" class="form-label">وصف المطعم (بالإنجليزية)</label>
                        <textarea id="description_en" name="description_en" class="form-control" rows="3" 
                                  placeholder="Brief description about your restaurant"><?php echo isset($_POST['description_en']) ? htmlspecialchars($_POST['description_en']) : ''; ?></textarea>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="phone" class="form-label">رقم الهاتف</label>
                        <input type="tel" id="phone" name="phone" class="form-control" 
                               placeholder="+966 50 123 4567" 
                               value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="address" class="form-label">العنوان (بالعربية)</label>
                        <input type="text" id="address" name="address" class="form-control" 
                               placeholder="الرياض، المملكة العربية السعودية" 
                               value="<?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?>">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="address_en" class="form-label">العنوان (بالإنجليزية)</label>
                    <input type="text" id="address_en" name="address_en" class="form-control" 
                           placeholder="Riyadh, Saudi Arabia" 
                           value="<?php echo isset($_POST['address_en']) ? htmlspecialchars($_POST['address_en']) : ''; ?>">
                </div>
                
                <!-- اختيار الباقة -->
                <div class="form-group">
                    <label class="form-label">اختر باقة الاشتراك *</label>
                    <div class="plans-grid">
                        <?php foreach ($plans as $index => $plan): ?>
                            <div class="plan-card <?php echo $index === 1 ? 'featured' : ''; ?>" onclick="selectPlan(<?php echo $plan['id']; ?>)">
                                <?php if ($index === 1): ?>
                                    <div class="plan-badge">الأكثر شعبية</div>
                                <?php endif; ?>
                                
                                <input type="radio" name="plan_id" value="<?php echo $plan['id']; ?>" class="plan-radio" id="plan_<?php echo $plan['id']; ?>" <?php echo $index === 1 ? 'checked' : ''; ?>>
                                
                                <div class="plan-name"><?php echo htmlspecialchars($plan['name_ar']); ?></div>
                                <div class="plan-price">$<?php echo number_format($plan['price'], 0); ?></div>
                                <div class="plan-period">شهرياً</div>
                                
                                <ul class="plan-features">
                                    <?php 
                                    $features = json_decode($plan['features'], true);
                                    $feature_names = [
                                        'internal_orders' => 'طلبات داخلية',
                                        'external_orders' => 'طلبات خارجية',
                                        'basic_stats' => 'إحصائيات أساسية',
                                        'advanced_stats' => 'إحصائيات متقدمة',
                                        'custom_design' => 'تصميم مخصص',
                                        'custom_qr' => 'QR Code مخصص',
                                        'online_payment' => 'دفع إلكتروني'
                                    ];
                                    
                                    foreach ($features as $feature) {
                                        if (isset($feature_names[$feature])) {
                                            echo '<li>' . $feature_names[$feature] . '</li>';
                                        }
                                    }
                                    
                                    // إضافة حد المنتجات
                                    if ($plan['products_limit'] == -1) {
                                        echo '<li>منتجات غير محدودة</li>';
                                    } else {
                                        echo '<li>' . $plan['products_limit'] . ' منتج</li>';
                                    }
                                    ?>
                                </ul>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <div class="setup-actions">
                    <button type="submit" class="btn btn-primary">إنشاء المطعم</button>
                    <a href="../auth/logout.php" class="btn btn-secondary">إلغاء</a>
                </div>
            </form>
        </div>
    </div>
    
    <script src="../assets/js/main.js"></script>
    <script>
        function selectPlan(planId) {
            // إزالة التحديد من جميع البطاقات
            document.querySelectorAll('.plan-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // تحديد البطاقة المختارة
            event.currentTarget.classList.add('selected');
            
            // تحديد الراديو بوتن
            document.getElementById('plan_' + planId).checked = true;
        }
        
        // تحديد البطاقة المختارة افتراضياً
        document.addEventListener('DOMContentLoaded', function() {
            const checkedRadio = document.querySelector('input[name="plan_id"]:checked');
            if (checkedRadio) {
                const planCard = checkedRadio.closest('.plan-card');
                if (planCard) {
                    planCard.classList.add('selected');
                }
            }
        });
    </script>
</body>
</html>
