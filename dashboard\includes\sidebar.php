<?php
// الحصول على الصفحة الحالية لتحديد الرابط النشط
$current_page = basename($_SERVER['PHP_SELF']);
$current_dir = basename(dirname($_SERVER['PHP_SELF']));

function isActive($page, $dir = '') {
    global $current_page, $current_dir;
    
    if ($dir) {
        return $current_dir === $dir ? 'active' : '';
    }
    
    return $current_page === $page ? 'active' : '';
}
?>

<div class="sidebar">
    <div class="sidebar-header">
        <div class="sidebar-logo">
            <a href="../dashboard/" style="text-decoration: none; color: inherit;">
                <?php echo SITE_NAME; ?>
            </a>
        </div>
    </div>
    
    <nav class="sidebar-nav">
        <div class="nav-item">
            <a href="../dashboard/" class="nav-link <?php echo isActive('index.php'); ?>">
                <span class="nav-icon">🏠</span>
                <span class="nav-text">الرئيسية</span>
            </a>
        </div>
        
        <?php if (isset($user_data) && $user_data['restaurant_id']): ?>
            <div class="nav-item">
                <a href="../dashboard/products/" class="nav-link <?php echo isActive('', 'products'); ?>">
                    <span class="nav-icon">🍽️</span>
                    <span class="nav-text">المنتجات</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="../dashboard/categories/" class="nav-link <?php echo isActive('', 'categories'); ?>">
                    <span class="nav-icon">📂</span>
                    <span class="nav-text">الفئات</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="../dashboard/orders/" class="nav-link <?php echo isActive('', 'orders'); ?>">
                    <span class="nav-icon">📋</span>
                    <span class="nav-text">الطلبات</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="../dashboard/analytics/" class="nav-link <?php echo isActive('', 'analytics'); ?>">
                    <span class="nav-icon">📊</span>
                    <span class="nav-text">الإحصائيات</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="../dashboard/qr-code.php" class="nav-link <?php echo isActive('qr-code.php'); ?>">
                    <span class="nav-icon">📱</span>
                    <span class="nav-text">QR Code</span>
                </a>
            </div>
            
            <div class="nav-item">
                <a href="../dashboard/menu-preview.php" class="nav-link <?php echo isActive('menu-preview.php'); ?>">
                    <span class="nav-icon">👁️</span>
                    <span class="nav-text">معاينة المنيو</span>
                </a>
            </div>
        <?php endif; ?>
        
        <div class="nav-item">
            <a href="../dashboard/restaurant/" class="nav-link <?php echo isActive('', 'restaurant'); ?>">
                <span class="nav-icon">🏪</span>
                <span class="nav-text">إعدادات المطعم</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="../dashboard/subscription.php" class="nav-link <?php echo isActive('subscription.php'); ?>">
                <span class="nav-icon">💳</span>
                <span class="nav-text">الاشتراك</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="../dashboard/profile.php" class="nav-link <?php echo isActive('profile.php'); ?>">
                <span class="nav-icon">👤</span>
                <span class="nav-text">الملف الشخصي</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="../dashboard/settings.php" class="nav-link <?php echo isActive('settings.php'); ?>">
                <span class="nav-icon">⚙️</span>
                <span class="nav-text">الإعدادات</span>
            </a>
        </div>
        
        <div class="nav-item">
            <a href="../dashboard/help.php" class="nav-link <?php echo isActive('help.php'); ?>">
                <span class="nav-icon">❓</span>
                <span class="nav-text">المساعدة</span>
            </a>
        </div>
        
        <hr style="margin: 1rem 0; border: none; border-top: 1px solid var(--border-color);">
        
        <div class="nav-item">
            <a href="../auth/logout.php" class="nav-link" style="color: var(--error-color);">
                <span class="nav-icon">🚪</span>
                <span class="nav-text">تسجيل الخروج</span>
            </a>
        </div>
    </nav>
</div>

<style>
/* تنسيقات إضافية للشريط الجانبي */
.sidebar {
    background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
}

.nav-item {
    position: relative;
}

.nav-link {
    position: relative;
    overflow: hidden;
}

.nav-link::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.1));
    transition: width 0.3s ease;
}

.nav-link:hover::before {
    width: 100%;
}

.nav-text {
    position: relative;
    z-index: 1;
}

.nav-icon {
    position: relative;
    z-index: 1;
    transition: transform 0.3s ease;
}

.nav-link:hover .nav-icon {
    transform: scale(1.1);
}

.nav-link.active {
    background: linear-gradient(90deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
    box-shadow: inset 3px 0 0 var(--primary-color);
}

.nav-link.active .nav-icon {
    transform: scale(1.1);
}

/* تأثيرات متقدمة */
.sidebar-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    position: relative;
    overflow: hidden;
}

.sidebar-header::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(180deg); }
}

.sidebar-logo a {
    position: relative;
    z-index: 1;
    display: block;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.show {
        transform: translateX(0);
    }
    
    .nav-link {
        padding: 1.25rem 1.5rem;
        font-size: 1.1rem;
    }
    
    .nav-icon {
        font-size: 1.3rem;
    }
}

/* تأثيرات التمرير */
.sidebar-nav {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-color) transparent;
}

.sidebar-nav::-webkit-scrollbar {
    width: 4px;
}

.sidebar-nav::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar-nav::-webkit-scrollbar-thumb {
    background: var(--primary-color);
    border-radius: 2px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: var(--secondary-color);
}
</style>
