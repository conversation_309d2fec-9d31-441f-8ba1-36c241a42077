<?php
/**
 * إعدادات خاصة بالاستضافة المشتركة
 */

// إعدادات PHP للاستضافة المشتركة
ini_set('display_errors', 0);
ini_set('log_errors', 1);
ini_set('error_log', dirname(__FILE__) . '/../logs/error.log');

// إعدادات الذاكرة والوقت
ini_set('memory_limit', '256M');
ini_set('max_execution_time', 300);
ini_set('max_input_time', 300);

// إعدادات الرفع
ini_set('upload_max_filesize', '10M');
ini_set('post_max_size', '12M');
ini_set('max_file_uploads', 20);

// إعدادات الجلسات
ini_set('session.cookie_lifetime', 0);
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 1);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

// إعدادات الأمان
ini_set('expose_php', 0);
ini_set('allow_url_fopen', 0);
ini_set('allow_url_include', 0);

// تعيين المنطقة الزمنية
date_default_timezone_set('Asia/Riyadh');

// إعدادات خاصة بالاستضافة المشتركة
define('SHARED_HOSTING', true);
define('DEBUG_MODE', false);
define('LOG_ERRORS', true);

// مسارات مخصصة للاستضافة المشتركة
define('LOG_PATH', dirname(__FILE__) . '/../logs/');
define('CACHE_PATH', dirname(__FILE__) . '/../cache/');
define('TEMP_PATH', dirname(__FILE__) . '/../temp/');

// إنشاء المجلدات المطلوبة إذا لم تكن موجودة
$required_dirs = [LOG_PATH, CACHE_PATH, TEMP_PATH];
foreach ($required_dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        
        // إنشاء ملف .htaccess لحماية المجلد
        file_put_contents($dir . '.htaccess', "Require all denied\nOptions -Indexes");
    }
}

// دالة لتسجيل الأخطاء
function log_error($message, $file = '', $line = '') {
    if (LOG_ERRORS) {
        $timestamp = date('Y-m-d H:i:s');
        $log_message = "[{$timestamp}] ";
        
        if ($file && $line) {
            $log_message .= "{$file}:{$line} - ";
        }
        
        $log_message .= $message . PHP_EOL;
        
        error_log($log_message, 3, LOG_PATH . 'app.log');
    }
}

// دالة للتحقق من متطلبات الاستضافة المشتركة
function check_hosting_requirements() {
    $requirements = [
        'PHP Version >= 7.4' => version_compare(PHP_VERSION, '7.4.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'GD Extension' => extension_loaded('gd'),
        'cURL Extension' => extension_loaded('curl'),
        'JSON Extension' => extension_loaded('json'),
        'mbstring Extension' => extension_loaded('mbstring'),
        'OpenSSL Extension' => extension_loaded('openssl'),
        'Uploads Directory Writable' => is_writable(dirname(__FILE__) . '/../uploads/'),
        'Config Directory Writable' => is_writable(dirname(__FILE__)),
    ];
    
    $missing = [];
    foreach ($requirements as $requirement => $status) {
        if (!$status) {
            $missing[] = $requirement;
        }
    }
    
    if (!empty($missing)) {
        log_error('متطلبات مفقودة: ' . implode(', ', $missing));
        return false;
    }
    
    return true;
}

// دالة لتنظيف الملفات المؤقتة
function cleanup_temp_files() {
    $temp_dir = TEMP_PATH;
    if (is_dir($temp_dir)) {
        $files = glob($temp_dir . '*');
        $now = time();
        
        foreach ($files as $file) {
            if (is_file($file) && ($now - filemtime($file)) > 3600) { // حذف الملفات الأقدم من ساعة
                unlink($file);
            }
        }
    }
}

// دالة لضغط الإخراج
function enable_compression() {
    if (!ob_get_level() && extension_loaded('zlib') && !ini_get('zlib.output_compression')) {
        ob_start('ob_gzhandler');
    }
}

// دالة للتحقق من حالة قاعدة البيانات
function check_database_connection() {
    try {
        global $pdo;
        $stmt = $pdo->query('SELECT 1');
        return true;
    } catch (PDOException $e) {
        log_error('خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage());
        return false;
    }
}

// تشغيل الفحوصات الأساسية
if (!check_hosting_requirements()) {
    if (DEBUG_MODE) {
        die('بعض متطلبات النظام غير متوفرة. يرجى مراجعة سجل الأخطاء.');
    }
}

// تنظيف الملفات المؤقتة (مرة واحدة كل 100 طلب)
if (rand(1, 100) === 1) {
    cleanup_temp_files();
}

// تفعيل الضغط
enable_compression();

// معالج الأخطاء المخصص
set_error_handler(function($severity, $message, $file, $line) {
    if (!(error_reporting() & $severity)) {
        return false;
    }
    
    log_error($message, $file, $line);
    
    if (DEBUG_MODE) {
        echo "<b>خطأ:</b> $message في <b>$file</b> السطر <b>$line</b><br>";
    }
    
    return true;
});

// معالج الاستثناءات المخصص
set_exception_handler(function($exception) {
    log_error('استثناء غير معالج: ' . $exception->getMessage(), $exception->getFile(), $exception->getLine());
    
    if (DEBUG_MODE) {
        echo "<b>استثناء:</b> " . $exception->getMessage() . "<br>";
        echo "<b>الملف:</b> " . $exception->getFile() . "<br>";
        echo "<b>السطر:</b> " . $exception->getLine() . "<br>";
    } else {
        header("HTTP/1.0 500 Internal Server Error");
        include dirname(__FILE__) . '/../public/500.php';
    }
});

// تسجيل بداية الجلسة
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
        log_error('خطأ فادح: ' . $error['message'], $error['file'], $error['line']);
        
        if (!DEBUG_MODE) {
            header("HTTP/1.0 500 Internal Server Error");
            include dirname(__FILE__) . '/../public/500.php';
        }
    }
});
?>
