<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// إعادة توجيه المستخدم المسجل دخوله
if (is_logged_in()) {
    redirect('../dashboard/');
}

$error = '';
$success = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $first_name = sanitize_input($_POST['first_name']);
    $last_name = sanitize_input($_POST['last_name']);
    $email = sanitize_input($_POST['email']);
    $phone = sanitize_input($_POST['phone']);
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    $terms = isset($_POST['terms']);
    
    // التحقق من البيانات
    if (empty($first_name) || empty($last_name) || empty($email) || empty($password)) {
        $error = 'جميع الحقول المطلوبة يجب ملؤها';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error = 'البريد الإلكتروني غير صحيح';
    } elseif (strlen($password) < 8) {
        $error = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    } elseif ($password !== $confirm_password) {
        $error = 'كلمة المرور غير متطابقة';
    } elseif (!$terms) {
        $error = 'يجب الموافقة على الشروط والأحكام';
    } else {
        try {
            // التحقق من وجود البريد الإلكتروني
            $stmt = $pdo->prepare("SELECT id FROM users WHERE email = ?");
            $stmt->execute([$email]);
            
            if ($stmt->fetch()) {
                $error = 'البريد الإلكتروني مستخدم بالفعل';
            } else {
                // إنشاء الحساب
                $hashed_password = password_hash($password, PASSWORD_DEFAULT);
                $verification_token = bin2hex(random_bytes(32));
                
                $stmt = $pdo->prepare("INSERT INTO users (first_name, last_name, email, phone, password, verification_token) VALUES (?, ?, ?, ?, ?, ?)");
                $stmt->execute([$first_name, $last_name, $email, $phone, $hashed_password, $verification_token]);
                
                $user_id = $pdo->lastInsertId();
                
                // إرسال بريد التفعيل (محاكاة)
                // send_verification_email($email, $verification_token);
                
                // تسجيل النشاط
                log_activity($user_id, null, 'register', 'تسجيل حساب جديد');
                
                $success = 'تم إنشاء حسابك بنجاح! يمكنك الآن تسجيل الدخول.';
                
                // تسجيل الدخول التلقائي
                $_SESSION['user_id'] = $user_id;
                $_SESSION['user_email'] = $email;
                $_SESSION['user_name'] = $first_name . ' ' . $last_name;
                
                // إعادة التوجيه بعد 2 ثانية
                header("refresh:2;url=../dashboard/setup.php");
            }
        } catch (PDOException $e) {
            $error = 'حدث خطأ في النظام';
        }
    }
}

function log_activity($user_id, $restaurant_id, $action, $description) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("INSERT INTO activity_logs (user_id, restaurant_id, action, description, ip_address, user_agent) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            $user_id,
            $restaurant_id,
            $action,
            $description,
            $_SERVER['REMOTE_ADDR'] ?? '',
            $_SERVER['HTTP_USER_AGENT'] ?? ''
        ]);
    } catch (PDOException $e) {
        // تجاهل الخطأ
    }
}
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إنشاء حساب جديد - <?php echo SITE_NAME; ?></title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <style>
        .auth-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem;
        }
        
        .auth-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 500px;
        }
        
        .auth-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .auth-logo {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }
        
        .auth-subtitle {
            color: var(--text-light);
            font-size: 1.1rem;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .form-check {
            display: flex;
            align-items: flex-start;
            gap: 0.5rem;
            margin: 1rem 0;
        }
        
        .form-check input[type="checkbox"] {
            width: auto;
            margin-top: 0.25rem;
        }
        
        .form-check label {
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .auth-links {
            text-align: center;
            margin-top: 2rem;
        }
        
        .auth-links a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }
        
        .auth-links a:hover {
            text-decoration: underline;
        }
        
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="auth-logo"><?php echo SITE_NAME; ?></div>
                <div class="auth-subtitle">إنشاء حساب جديد</div>
            </div>
            
            <?php if ($error): ?>
                <div class="alert alert-error"><?php echo $error; ?></div>
            <?php endif; ?>
            
            <?php if ($success): ?>
                <div class="alert alert-success"><?php echo $success; ?></div>
            <?php endif; ?>
            
            <form method="POST" action="">
                <div class="form-row">
                    <div class="form-group">
                        <label for="first_name" class="form-label">الاسم الأول *</label>
                        <input type="text" id="first_name" name="first_name" class="form-control" 
                               placeholder="الاسم الأول" required 
                               value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>">
                    </div>
                    
                    <div class="form-group">
                        <label for="last_name" class="form-label">الاسم الأخير *</label>
                        <input type="text" id="last_name" name="last_name" class="form-control" 
                               placeholder="الاسم الأخير" required 
                               value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>">
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="email" class="form-label">البريد الإلكتروني *</label>
                    <input type="email" id="email" name="email" class="form-control" 
                           placeholder="أدخل بريدك الإلكتروني" required 
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>
                
                <div class="form-group">
                    <label for="phone" class="form-label">رقم الهاتف</label>
                    <input type="tel" id="phone" name="phone" class="form-control" 
                           placeholder="رقم الهاتف (اختياري)" 
                           value="<?php echo isset($_POST['phone']) ? htmlspecialchars($_POST['phone']) : ''; ?>">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="password" class="form-label">كلمة المرور *</label>
                        <input type="password" id="password" name="password" class="form-control" 
                               placeholder="كلمة المرور (8 أحرف على الأقل)" required>
                    </div>
                    
                    <div class="form-group">
                        <label for="confirm_password" class="form-label">تأكيد كلمة المرور *</label>
                        <input type="password" id="confirm_password" name="confirm_password" class="form-control" 
                               placeholder="تأكيد كلمة المرور" required>
                    </div>
                </div>
                
                <div class="form-check">
                    <input type="checkbox" id="terms" name="terms" value="1" required>
                    <label for="terms">
                        أوافق على <a href="../terms.php" target="_blank">الشروط والأحكام</a> 
                        و <a href="../privacy.php" target="_blank">سياسة الخصوصية</a>
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary w-100">إنشاء الحساب</button>
            </form>
            
            <div class="auth-links">
                <p>لديك حساب بالفعل؟ <a href="login.php">تسجيل الدخول</a></p>
                <p><a href="../">العودة للصفحة الرئيسية</a></p>
            </div>
        </div>
    </div>
    
    <script src="../assets/js/main.js"></script>
</body>
</html>
