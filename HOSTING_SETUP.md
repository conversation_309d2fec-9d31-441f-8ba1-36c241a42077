# دليل إعداد منصة Menuz على الاستضافة المشتركة

## معلومات الاستضافة الحالية

- **الرابط**: https://bag.meedps.com
- **قاعدة البيانات**: meedpsco_Menu
- **مستخدم قاعدة البيانات**: meedpsco_Menu
- **كلمة مرور قاعدة البيانات**: meedpsco_Menu

## خطوات التثبيت

### 1. رفع الملفات
قم برفع جميع ملفات المشروع إلى المجلد الجذر للموقع (public_html أو www)

### 2. إعداد الصلاحيات
تأكد من أن المجلدات التالية لها صلاحيات الكتابة (755):
```
uploads/
config/
logs/
cache/
temp/
```

### 3. تشغيل التثبيت
1. افتح المتصفح وانتقل إلى: https://bag.meedps.com/install.php
2. ستظهر صفحة التثبيت مع إعدادات قاعدة البيانات المحددة مسبقاً
3. اضغط على "تثبيت المنصة"
4. انتظر حتى اكتمال التثبيت

### 4. التحقق من التثبيت
بعد اكتمال التثبيت:
1. انتقل إلى: https://bag.meedps.com
2. يجب أن تظهر الصفحة الرئيسية للمنصة
3. اضغط على "إنشاء حساب جديد" لبدء الاستخدام

## الملفات المهمة للاستضافة المشتركة

### ملفات الحماية
- `.htaccess` - الحماية الرئيسية والإعدادات
- `config/.htaccess` - حماية ملفات التكوين
- `database/.htaccess` - حماية ملفات قاعدة البيانات
- `uploads/.htaccess` - حماية مجلد الرفع
- `robots.txt` - توجيهات محركات البحث

### ملفات التكوين
- `config/database.php` - إعدادات قاعدة البيانات
- `config/hosting.php` - إعدادات خاصة بالاستضافة المشتركة

### صفحات الأخطاء
- `public/404.php` - صفحة عدم وجود الصفحة
- `public/500.php` - صفحة خطأ الخادم

## المميزات المحسنة للاستضافة المشتركة

### الأمان
- ✅ فرض HTTPS
- ✅ حماية ملفات التكوين
- ✅ منع تنفيذ PHP في مجلد الرفع
- ✅ حماية من hotlinking
- ✅ منع البوتات الضارة
- ✅ إعدادات أمان HTTP headers

### الأداء
- ✅ ضغط الملفات (Gzip)
- ✅ تخزين مؤقت للملفات الثابتة
- ✅ تحسين إعدادات PHP
- ✅ تنظيف الملفات المؤقتة تلقائياً

### إدارة الأخطاء
- ✅ تسجيل الأخطاء في ملفات منفصلة
- ✅ صفحات أخطاء مخصصة
- ✅ معالجة الاستثناءات
- ✅ إخفاء تفاصيل الأخطاء في الإنتاج

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ 500 - Internal Server Error
**الأسباب المحتملة:**
- صلاحيات ملفات خاطئة
- خطأ في ملف .htaccess
- نفاد الذاكرة

**الحلول:**
```bash
# تعيين صلاحيات الملفات
chmod 644 *.php
chmod 644 .htaccess
chmod 755 uploads/
chmod 755 config/
```

#### 2. خطأ في الاتصال بقاعدة البيانات
**التحقق من:**
- صحة بيانات الاتصال
- تفعيل قاعدة البيانات من لوحة تحكم الاستضافة
- صلاحيات المستخدم

#### 3. مشاكل رفع الصور
**التحقق من:**
- صلاحيات مجلد uploads (755)
- حجم الملف (أقل من 10MB)
- نوع الملف (jpg, png, gif)

#### 4. مشاكل الجلسات
**الحلول:**
- التأكد من تفعيل sessions في PHP
- التحقق من صلاحيات مجلد tmp

### فحص سجلات الأخطاء
```bash
# عرض آخر الأخطاء
tail -f logs/app.log
tail -f logs/error.log
```

## إعدادات إضافية

### تحسين الأداء
1. **تفعيل OPcache** (إذا كان متاحاً):
```php
opcache.enable=1
opcache.memory_consumption=128
opcache.max_accelerated_files=4000
```

2. **تحسين MySQL**:
```sql
SET GLOBAL innodb_buffer_pool_size = 128M;
SET GLOBAL query_cache_size = 32M;
```

### النسخ الاحتياطي
```bash
# نسخ احتياطي لقاعدة البيانات
mysqldump -u meedpsco_Menu -p meedpsco_Menu > backup_$(date +%Y%m%d).sql

# نسخ احتياطي للملفات
tar -czf backup_files_$(date +%Y%m%d).tar.gz uploads/ config/
```

## المراقبة والصيانة

### مراقبة الأداء
- مراجعة سجلات الأخطاء يومياً
- مراقبة استخدام المساحة
- فحص سرعة تحميل الصفحات

### الصيانة الدورية
- تنظيف الملفات المؤقتة
- تحديث كلمات المرور
- مراجعة إعدادات الأمان
- النسخ الاحتياطي الأسبوعي

## الدعم الفني

### معلومات مفيدة للدعم
- **إصدار PHP**: يفضل 7.4 أو أحدث
- **إصدار MySQL**: 5.7 أو أحدث
- **الذاكرة المطلوبة**: 256MB على الأقل
- **المساحة المطلوبة**: 100MB على الأقل

### جهات الاتصال
- **الاستضافة**: meedps.com
- **المطور**: [معلومات الاتصال]

## ملاحظات مهمة

⚠️ **تحذيرات:**
- لا تحذف ملفات .htaccess
- لا تغير صلاحيات الملفات بشكل عشوائي
- احتفظ بنسخة احتياطية قبل أي تحديث

✅ **نصائح:**
- راجع سجلات الأخطاء بانتظام
- اختبر الموقع بعد أي تغيير
- استخدم HTTPS دائماً
- فعّل التحديثات التلقائية للأمان

---

**تم إعداد هذا الدليل خصيصاً لاستضافة bag.meedps.com**
