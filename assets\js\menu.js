// JavaScript خاص بصفحة المنيو

// متغيرات عامة
let cart = [];
let isCartOpen = false;

// تهيئة صفحة المنيو
document.addEventListener('DOMContentLoaded', function() {
    initializeMenu();
    loadCartFromStorage();
    updateCartDisplay();
    initializeCategoryNavigation();
    initializeProductCards();
});

/**
 * تهيئة المنيو
 */
function initializeMenu() {
    // تحديث عداد السلة
    updateCartCount();
    
    // تهيئة التمرير السلس للفئات
    initializeSmoothScroll();
    
    // تهيئة lazy loading للصور
    initializeLazyLoading();
    
    // تحديث الرابط النشط في قائمة الفئات
    updateActiveCategoryLink();
}

/**
 * إضافة منتج للسلة
 */
function addToCart(productId) {
    const product = productsData[productId];
    if (!product) return;
    
    // البحث عن المنتج في السلة
    const existingItem = cart.find(item => item.id === productId);
    
    if (existingItem) {
        existingItem.quantity += 1;
    } else {
        cart.push({
            id: productId,
            name: product.name,
            price: product.price,
            image: product.image,
            quantity: 1
        });
    }
    
    // تحديث العرض
    updateCartDisplay();
    updateCartCount();
    saveCartToStorage();
    
    // إظهار تأثير بصري
    showAddToCartAnimation(productId);
    
    // إظهار إشعار
    showNotification(restaurantData.lang === 'ar' ? 'تم إضافة المنتج للطلب' : 'Product added to order', 'success', 2000);
}

/**
 * إزالة منتج من السلة
 */
function removeFromCart(productId) {
    const itemIndex = cart.findIndex(item => item.id === productId);
    if (itemIndex > -1) {
        cart.splice(itemIndex, 1);
        updateCartDisplay();
        updateCartCount();
        saveCartToStorage();
    }
}

/**
 * تحديث كمية منتج في السلة
 */
function updateCartItemQuantity(productId, change) {
    const item = cart.find(item => item.id === productId);
    if (!item) return;
    
    item.quantity += change;
    
    if (item.quantity <= 0) {
        removeFromCart(productId);
    } else {
        updateCartDisplay();
        updateCartCount();
        saveCartToStorage();
    }
}

/**
 * تحديث عرض السلة
 */
function updateCartDisplay() {
    const cartItems = document.getElementById('cart-items');
    const cartTotal = document.getElementById('cart-total');
    const checkoutBtn = document.querySelector('.checkout-btn');
    
    if (cart.length === 0) {
        cartItems.innerHTML = `
            <div class="empty-cart">
                <p>${restaurantData.lang === 'ar' ? 'لا توجد منتجات في طلبك' : 'No items in your order'}</p>
            </div>
        `;
        cartTotal.textContent = '0.00';
        checkoutBtn.disabled = true;
    } else {
        let total = 0;
        let itemsHTML = '';
        
        cart.forEach(item => {
            const itemTotal = item.price * item.quantity;
            total += itemTotal;
            
            itemsHTML += `
                <div class="cart-item">
                    ${item.image ? `
                        <div class="cart-item-image">
                            <img src="${UPLOAD_PATH}${item.image}" alt="${item.name}">
                        </div>
                    ` : ''}
                    <div class="cart-item-info">
                        <div class="cart-item-name">${item.name}</div>
                        <div class="cart-item-price">$${itemTotal.toFixed(2)}</div>
                        <div class="cart-item-controls">
                            <button class="quantity-btn" onclick="updateCartItemQuantity(${item.id}, -1)">-</button>
                            <span class="quantity-display">${item.quantity}</span>
                            <button class="quantity-btn" onclick="updateCartItemQuantity(${item.id}, 1)">+</button>
                            <button class="remove-btn" onclick="removeFromCart(${item.id})" style="margin-right: 0.5rem; color: var(--error-color); background: none; border: none; cursor: pointer;">🗑️</button>
                        </div>
                    </div>
                </div>
            `;
        });
        
        cartItems.innerHTML = itemsHTML;
        cartTotal.textContent = total.toFixed(2);
        checkoutBtn.disabled = false;
    }
}

/**
 * تحديث عداد السلة
 */
function updateCartCount() {
    const cartCount = document.querySelector('.cart-count');
    const totalItems = cart.reduce((sum, item) => sum + item.quantity, 0);
    
    cartCount.textContent = totalItems;
    
    if (totalItems > 0) {
        cartCount.classList.add('show');
    } else {
        cartCount.classList.remove('show');
    }
}

/**
 * تبديل عرض السلة
 */
function toggleCart() {
    const cartSidebar = document.getElementById('cart-sidebar');
    const cartOverlay = document.getElementById('cart-overlay');
    
    isCartOpen = !isCartOpen;
    
    if (isCartOpen) {
        cartSidebar.classList.add('open');
        cartOverlay.classList.add('show');
        document.body.style.overflow = 'hidden';
    } else {
        cartSidebar.classList.remove('open');
        cartOverlay.classList.remove('show');
        document.body.style.overflow = '';
    }
}

/**
 * المتابعة للدفع
 */
function proceedToCheckout() {
    if (cart.length === 0) return;
    
    // إنشاء نموذج الطلب
    const orderData = {
        restaurant_id: restaurantData.id,
        items: cart,
        total: cart.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    };
    
    // حفظ بيانات الطلب في التخزين المحلي
    localStorage.setItem('orderData', JSON.stringify(orderData));
    
    // إعادة التوجيه لصفحة الطلب
    window.location.href = `order.php?restaurant=${restaurantData.slug}`;
}

/**
 * حفظ السلة في التخزين المحلي
 */
function saveCartToStorage() {
    localStorage.setItem(`cart_${restaurantData.id}`, JSON.stringify(cart));
}

/**
 * تحميل السلة من التخزين المحلي
 */
function loadCartFromStorage() {
    const savedCart = localStorage.getItem(`cart_${restaurantData.id}`);
    if (savedCart) {
        cart = JSON.parse(savedCart);
    }
}

/**
 * تهيئة التنقل بين الفئات
 */
function initializeCategoryNavigation() {
    const categoryLinks = document.querySelectorAll('.category-link');
    
    categoryLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                const headerHeight = document.querySelector('.restaurant-header').offsetHeight;
                const categoriesNavHeight = document.querySelector('.categories-nav')?.offsetHeight || 0;
                const offset = headerHeight + categoriesNavHeight + 20;
                
                window.scrollTo({
                    top: targetElement.offsetTop - offset,
                    behavior: 'smooth'
                });
            }
        });
    });
}

/**
 * تحديث الرابط النشط في قائمة الفئات
 */
function updateActiveCategoryLink() {
    const categoryLinks = document.querySelectorAll('.category-link');
    const categorySections = document.querySelectorAll('.category-section');
    
    if (categoryLinks.length === 0) return;
    
    window.addEventListener('scroll', function() {
        const scrollPosition = window.scrollY + window.innerHeight / 2;
        
        let activeSection = null;
        categorySections.forEach(section => {
            const sectionTop = section.offsetTop;
            const sectionBottom = sectionTop + section.offsetHeight;
            
            if (scrollPosition >= sectionTop && scrollPosition <= sectionBottom) {
                activeSection = section;
            }
        });
        
        // تحديث الروابط النشطة
        categoryLinks.forEach(link => {
            link.classList.remove('active');
            
            if (activeSection) {
                const targetId = link.getAttribute('href').substring(1);
                if (targetId === activeSection.id) {
                    link.classList.add('active');
                }
            }
        });
    });
}

/**
 * تهيئة التمرير السلس
 */
function initializeSmoothScroll() {
    // إضافة تأثير التمرير السلس لجميع الروابط الداخلية
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href').substring(1);
            const targetElement = document.getElementById(targetId);
            
            if (targetElement) {
                targetElement.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

/**
 * تهيئة lazy loading للصور
 */
function initializeLazyLoading() {
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    observer.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[loading="lazy"]').forEach(img => {
            imageObserver.observe(img);
        });
    }
}

/**
 * تهيئة بطاقات المنتجات
 */
function initializeProductCards() {
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        // إضافة تأثير hover
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
        
        // إضافة تأثير النقر
        card.addEventListener('click', function(e) {
            if (!e.target.classList.contains('add-to-cart-btn')) {
                // يمكن إضافة modal لعرض تفاصيل المنتج
                showProductDetails(this.dataset.productId);
            }
        });
    });
}

/**
 * عرض تفاصيل المنتج
 */
function showProductDetails(productId) {
    // يمكن تطوير modal لعرض تفاصيل المنتج
    console.log('عرض تفاصيل المنتج:', productId);
}

/**
 * إظهار تأثير إضافة للسلة
 */
function showAddToCartAnimation(productId) {
    const productCard = document.querySelector(`[data-product-id="${productId}"]`);
    const cartBtn = document.querySelector('.cart-btn');
    
    if (productCard && cartBtn) {
        // إنشاء عنصر متحرك
        const flyingItem = document.createElement('div');
        flyingItem.style.cssText = `
            position: fixed;
            width: 30px;
            height: 30px;
            background: var(--restaurant-primary);
            border-radius: 50%;
            z-index: 9999;
            pointer-events: none;
            transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        `;
        
        const productRect = productCard.getBoundingClientRect();
        const cartRect = cartBtn.getBoundingClientRect();
        
        flyingItem.style.left = productRect.left + productRect.width / 2 + 'px';
        flyingItem.style.top = productRect.top + productRect.height / 2 + 'px';
        
        document.body.appendChild(flyingItem);
        
        // تحريك العنصر للسلة
        setTimeout(() => {
            flyingItem.style.left = cartRect.left + cartRect.width / 2 + 'px';
            flyingItem.style.top = cartRect.top + cartRect.height / 2 + 'px';
            flyingItem.style.transform = 'scale(0)';
            flyingItem.style.opacity = '0';
        }, 50);
        
        // إزالة العنصر
        setTimeout(() => {
            document.body.removeChild(flyingItem);
        }, 850);
        
        // تأثير على زر السلة
        cartBtn.style.transform = 'scale(1.2)';
        setTimeout(() => {
            cartBtn.style.transform = 'scale(1)';
        }, 200);
    }
}

/**
 * مشاركة المنيو
 */
function shareMenu() {
    if (navigator.share) {
        navigator.share({
            title: restaurantData.name,
            text: restaurantData.lang === 'ar' ? 'شاهد منيو هذا المطعم الرائع' : 'Check out this amazing restaurant menu',
            url: window.location.href
        });
    } else {
        // نسخ الرابط للحافظة
        copyToClipboard(window.location.href);
        showNotification(restaurantData.lang === 'ar' ? 'تم نسخ رابط المنيو' : 'Menu link copied', 'success');
    }
}

/**
 * البحث في المنتجات
 */
function searchProducts(query) {
    const productCards = document.querySelectorAll('.product-card');
    const searchQuery = query.toLowerCase();
    
    productCards.forEach(card => {
        const productName = card.querySelector('.product-name').textContent.toLowerCase();
        const productDescription = card.querySelector('.product-description')?.textContent.toLowerCase() || '';
        
        if (productName.includes(searchQuery) || productDescription.includes(searchQuery)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

/**
 * تصفية المنتجات حسب السعر
 */
function filterByPrice(minPrice, maxPrice) {
    const productCards = document.querySelectorAll('.product-card');
    
    productCards.forEach(card => {
        const priceText = card.querySelector('.product-price').textContent;
        const price = parseFloat(priceText.replace('$', ''));
        
        if (price >= minPrice && price <= maxPrice) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

/**
 * إعادة تعيين التصفية
 */
function resetFilters() {
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach(card => {
        card.style.display = 'block';
    });
}

// إضافة مستمعي الأحداث للوحة المفاتيح
document.addEventListener('keydown', function(e) {
    // إغلاق السلة بالضغط على Escape
    if (e.key === 'Escape' && isCartOpen) {
        toggleCart();
    }
});

// تحديث السلة عند تغيير حجم النافذة
window.addEventListener('resize', function() {
    if (window.innerWidth > 768 && isCartOpen) {
        // إعادة تعيين موضع السلة للشاشات الكبيرة
        updateCartDisplay();
    }
});

// حفظ السلة قبل إغلاق الصفحة
window.addEventListener('beforeunload', function() {
    saveCartToStorage();
});
